{"name": "EasyAR.Sense.Sample.MegaBlock_Basic", "references": ["EasyAR.Sense", "EasyAR.Mega.Scene"], "includePlatforms": [], "excludePlatforms": [], "allowUnsafeCode": false, "overrideReferences": false, "precompiledReferences": [], "autoReferenced": true, "defineConstraints": [], "versionDefines": [{"name": "com.easyar.mega", "expression": "", "define": "EASYAR_ENABLE_MEGA"}], "noEngineReferences": false}