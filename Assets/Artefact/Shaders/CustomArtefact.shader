Shader "Custom/Artefact"
{
	Properties
	{
		_MainTex("Tint Color (RGB)", 2D) = "white" {}
		_Color("Color", Color) = (1,1,1,1)
		_Glossiness("Smoothness", Range(0,1)) = 0.5
		_Metallic("Metallic", Range(0,1)) = 0.0
		_Dist("Refraction Intensity", Float) = 1.0
		_RimColor("Rim Color", Color) = (0.26,0.19,0.16,0.0)
		_RimPower("Rim Power", Range(0.0,2.0)) = 1.5
		_Alpha("Alpha", Range(0,1)) = 1.0
	}
		SubShader
	{
		Tags { "RenderType" = "Transparent" "Queue" = "Transparent" }
		LOD 200

		Blend SrcAlpha OneMinusSrcAlpha // Alpha blending
			
		GrabPass { "_RefractionTex" }

		CGPROGRAM
		// Physically based Standard lighting model, and enable shadows on all light types
		#pragma surface surf Standard fullforwardshadows vertex:vert alpha:blend 

		// Use shader model 3.0 target, to get nicer looking lighting
		#pragma target 3.0

		#include "UnityCG.cginc"

		sampler2D _MainTex;
		float4 _MainTex_ST;
		sampler2D _RefractionTex;
		float4 _RefractionTex_TexelSize;

		struct Input
		{
			float2 uv_FirstTex:TEXCOORD0;
			float4 grabPos;
			float3 viewDir;
		};

		half _Glossiness;
		half _Metallic;
		half _Dist;
		fixed4 _Color;
		float4 _RimColor;
		float _RimPower;
		float _Alpha;

		// Add instancing support for this shader. You need to check 'Enable Instancing' on materials that use the shader.
		// See https://docs.unity3d.com/Manual/GPUInstancing.html for more information about instancing.
		// #pragma instancing_options assumeuniformscaling
		UNITY_INSTANCING_BUFFER_START(Props)
		// put more per-instance properties here
		UNITY_INSTANCING_BUFFER_END(Props)

		void vert(inout appdata_full v, out Input o)
		{
			UNITY_INITIALIZE_OUTPUT(Input, o);

			float4 pos = UnityObjectToClipPos(v.vertex);
			o.grabPos = ComputeGrabScreenPos(pos);
			o.uv_FirstTex = TRANSFORM_UV(0);
			//o.uv_FirstTex = v.uv;
			//o.uv_FirstTex = TRANSFORM_TEX(v.texcoord, _MainTex);
		}

		void surf(Input IN, inout SurfaceOutputStandard o)
		{
			float2 uv = IN.grabPos.xy / IN.grabPos.w;

			float2 norm = normalize(mul((float3x3)unity_WorldToCamera, o.Normal)).xy;

			fixed4 col = tex2D(_MainTex, IN.uv_FirstTex);

			half rim = 1.0 - saturate(dot(normalize(IN.viewDir), o.Normal));
			o.Emission = _RimColor.rgb * pow(rim, (2.0-_RimPower)) * _Alpha;

			o.Albedo = tex2D(_RefractionTex, uv - norm * 0.1 * _Dist) * _Color * col;

			o.Metallic = _Metallic;
			o.Smoothness = _Glossiness;
			o.Alpha = _Alpha;

		}
		ENDCG
	}
		FallBack "Diffuse"
}
