using UnityEngine;

namespace Com.SceneConsole.Arte
{
    /// <summary>
    /// 朝向摄像头约束
    /// </summary>
    public class CameraFacingBillboard : MonoBehaviour
    {
        public Vector3 axis = Vector3.up;

        [HideInInspector]
        public Camera m_Camera;

        private void Awake()
        {
            m_Camera = GameObject.Find("AR Camera").transform.GetComponent<Camera>();
        }

        //Orient the camera after all movement is completed this frame to avoid jittering
        void LateUpdate()
        {
            transform.LookAt(transform.position + m_Camera.transform.rotation * Vector3.forward,
                m_Camera.transform.rotation * axis);
        }
    }
}
