using System;
using System.Collections;

using UnityEngine;
using UnityEngine.Networking;
using UnityEngine.Video;

namespace Com.SceneConsole.Arte
{
    /// <summary>
    /// Arte预制件类
    /// </summary>
    public class ArtefactWarp : MonoBehaviour
    {
        public Artefact artefact;

        [HideInInspector]
        public string id;

        [HideInInspector]
        public int bizType;

        [HideInInspector]
        public bool initial;

        [HideInInspector]
        public string hexColor;

        [HideInInspector]
        public float initialHeight;

        [HideInInspector]
        public Vector3 initialPosition;

        [HideInInspector]
        public bool published = false;

        public enum LayoutMode
        {
            native = 0,
            unity,
        }

        [HideInInspector]
        public LayoutMode mode = LayoutMode.unity;

        public void InitialWithUnity(ArtefactUnity arti)
        {
            ColorUtility.TryParseHtmlString(arti.color, out Color color);

            id = arti.id;
            hexColor = arti.color;
            bizType = arti.bizType;
            initialHeight = arti.height;
            mode = (ArtefactWarp.LayoutMode)arti.layoutMode;
            artefact.Geometry = (ArteGeometry)(arti.shape);
            artefact.arteType = (Artefact.Type)arti.media;
            artefact.Color = color;
            artefact.Alpha = 1;
            artefact.Glow = 1;

            if (arti.content != null && arti.content.Length > 0)
            {
                artefact.imagePreview = Base64StringToTexture2D(arti.content);
            }

            artefact.UpdatePreviewUrls(arti.prePhotos);

            artefact.videoPreviewPath = arti.videoPath;
            if (arti.imagePath != null && arti.imagePath.Length > 0)
            {
                string[] arr = { arti.imagePath };
                artefact.UpdatePreviewUrls(arr);
            }

            if (mode == ArtefactWarp.LayoutMode.native)
            {
                artefact.Alpha = 0;
            }
            if (arti.initial)
            {
                initial = true;
                artefact.Alpha = 0;
                artefact.Scale = 0.3f;
            }
            else
            {
                artefact.Scale = 0.3f;
            }

        }

        public void InitialWithAnswer()
        {
            System.Random random = new System.Random();
            ArteGeometry[] geometrys = Enum.GetValues(typeof(ArteGeometry)) as ArteGeometry[];
            ArteStyle[] styles = Enum.GetValues(typeof(ArteStyle)) as ArteStyle[];
            artefact.Geometry = geometrys[random.Next(0, geometrys.Length)];
            artefact.arteType = Artefact.Type.text;
            ColorUtility.TryParseHtmlString(ColorWithStyle(styles[random.Next(0, styles.Length)]), out Color color);
            artefact.Color = color;
            artefact.Alpha = 0;
            artefact.Glow = 1;
            artefact.Scale = 0;
        }

        private string ColorWithStyle(ArteStyle style)
        {
            switch (style)
            {
                case ArteStyle.yellow:
                    return "#FCD783";
                case ArteStyle.red:
                    return "#B72542";
                case ArteStyle.pink:
                    return "#C745BC";
                case ArteStyle.blue:
                    return "#394EFF";
                case ArteStyle.orange:
                    return "#D7651D";
                case ArteStyle.azora:
                    return "#388292";
                case ArteStyle.dark:
                    return "#12113E";
                default:
                    return "#C745BC";
            }
        }

        public void Move(Vector3 camPos)
        {
            var pos = artefact.transform.position;
            Vector3 camera = new Vector3(camPos.x, camPos.y, camPos.z);
            Vector3 target = new Vector3(pos.x, pos.y, pos.z);
            float distance = Vector3.Distance(camera, target);
            float minDist = 1.5f;
            if (distance < minDist)
            {
                Vector2 vector = (new Vector2(pos.x, pos.z) - new Vector2(camPos.x, camPos.z)).normalized;
                artefact.initialPos.x += vector.x * (minDist - distance);
                artefact.initialPos.z += vector.y * (minDist - distance);
            }
        }

        public override bool Equals(object obj)
        {
            ArtefactWarp e = obj as ArtefactWarp;
            return id == e.id;
        }

        public override int GetHashCode()
        {
            return id.GetHashCode();
        }

        public static Texture2D Base64StringToTexture2D(string base64)
        {
            Texture2D tex = new Texture2D(1, 1);
            try
            {
                byte[] bytes = System.Convert.FromBase64String(base64);
                tex.LoadImage(bytes);
                tex.hideFlags = HideFlags.DontSave;
                tex.filterMode = FilterMode.Point;
                tex.wrapMode = TextureWrapMode.Clamp;
            }
            catch (System.Exception ex)
            {
                Debug.Log("Create Texture2D Failed!!");
                Debug.LogError(ex.Message);
            }
            return tex;
        }
    }
}
