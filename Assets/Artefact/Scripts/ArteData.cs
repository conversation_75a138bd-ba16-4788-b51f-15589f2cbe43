using System.Collections;
using System.Collections.Generic;

using UnityEngine;


namespace Com.SceneConsole.Arte
{

    public enum ArteGeometry
    {
        /// <summary>
        /// 四面体
        /// </summary>
        tetra = 0,
        /// <summary>
        /// 立方体
        /// </summary>
        cube,
        /// <summary>
        /// 八面体
        /// </summary>
        octa,
        /// <summary>
        /// 十二面体
        /// </summary>
        dodeca,
        /// <summary>
        /// 二十面体
        /// </summary>
        icosa,
        /// <summary>
        /// 三十二面体
        /// </summary>
        octa32,
    }

    public enum ArteStyle
    {
        yellow,
        red,
        pink,
        blue,
        orange,
        azora,
        dark,
    }

    [System.Serializable]
    public class ArteRawData
    {
        /// <summary>
        /// Arte类型
        /// </summary>
        public ArteGeometry geometryType;

        /// <summary>
        /// 网格材质
        /// </summary>
        public Mesh mesh;

        /// <summary>
        /// 关闭动画外轮廓
        /// </summary>
        public Texture outline;

        /// <summary>
        /// 关闭动画内轮廓
        /// </summary>
        public Texture fill;
    }
}

