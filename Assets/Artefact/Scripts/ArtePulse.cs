using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Com.SceneConsole.Arte
{
    /// <summary>
    /// Arte扩大动画
    /// </summary>
    public class ArtePulse : MonoBehaviour
    {

        public float baseScale = 1.0f;
        public float maxScale = 3.0f;
        public float speed = 1.0f;

        private Material material;

        void Start()
        {
            material = GetComponent<Renderer>().material;
        }

        void Update()
        {
            float value = baseScale + Mathf.Abs(Mathf.Sin(Time.time * speed) * (maxScale - baseScale));
            material.SetFloat("_ScaleX", value);
            material.SetFloat("_ScaleY", value);
        }

    }
}
