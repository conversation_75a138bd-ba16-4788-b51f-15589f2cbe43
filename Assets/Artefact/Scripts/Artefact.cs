using System;
using System.Collections;

using UnityEngine;
using UnityEngine.Video;
using UnityEngine.XR.ARFoundation;

using Random = UnityEngine.Random;

namespace Com.SceneConsole.Arte
{
    /// <summary>
    /// Arte基础类
    /// </summary>
    public class Artefact : MonoBehaviour
    {

        public static LayerMask Layer;
        public static string Tag = "Artefact";

        public enum Type
        {
            audio = 1,
            video,
            image,
            text,
            paint,
            mix,
        }

        public Type arteType = Type.text;

        /// <summary>
        /// 摄像机
        /// </summary>
        [HideInInspector]
        public Camera mainCamera;

        /// <summary>
        /// Arte类型
        /// </summary>
        public ArteGeometry Geometry
        {
            get => geometry;
            set
            {
                geometry = value;
                SetGeometry(value);
            }
        }

        /// <summary>
        /// 透明度 0 - 1
        /// </summary>
        public float Alpha
        {
            get => alpha;
            set
            {
                alpha = value;
                SetAlpha(value);
            }
        }

        /// <summary>
        /// 外发光 0 - 1
        /// </summary>
        public float Glow
        {
            get => glow; set
            {
                glow = value;
                SetGlow(value);
            }
        }

        /// <summary>
        /// 缩放 0 - 1
        /// </summary>
        public float Scale
        {
            get => scale; set
            {
                scale = value;
                SetScale(value);
            }
        }

        /// <summary>
        /// 颜色
        /// </summary>
        public Color Color
        {
            get => color; set
            {
                color = value;
                SetColor(value);
            }
        }

        /// <summary>
        /// Arte数据
        /// </summary>
        [SerializeField]
        private ArteRawData[] rawDatas;

        /// <summary>
        /// 文字预览
        /// </summary>
        [SerializeField]
        private VideoClip textPreview;

        /// <summary>
        /// 音频预览
        /// </summary>
        [SerializeField]
        private VideoClip audioPreview;

        /// <summary>
        /// 图片预览
        /// </summary>
        [HideInInspector]
        public Texture2D imagePreview;

        /// <summary>
        /// 视频预览
        /// </summary>
        [HideInInspector]
        public VideoClip videoPreview;

        /// <summary>
        /// 图片视频地址
        /// </summary>
        [HideInInspector]
        public string imagePreviewPath;

        /// <summary>
        /// 预览视频地址
        /// </summary>
        [HideInInspector]
        public string videoPreviewPath;

        /// <summary>
        /// 消失粒子
        /// </summary>
        [SerializeField]
        private GameObject dissolve;

        /// <summary>
        /// 消失遮罩
        /// </summary>
        [SerializeField]
        private GameObject mask;

        /// <summary>
        /// 旋转速度
        /// </summary>
        public float rotateSpeed;

        /// <summary>
        /// 是否旋转
        /// </summary>
        public bool isRotation = true;

        private ArteGeometry geometry = ArteGeometry.octa32;
        private ArteRawData rawData;
        private Color color = new Color32(123, 0, 159, 255);
        private float alpha = 1.0f;
        private float glow = 1.0f;
        private float scale = 1.0f;

        /// 漂浮动画初始位置
        [HideInInspector]
        public Vector3 initialPos;
        private float deltaTime;

        private bool isInitial;
        private bool isPublish;
        private bool isRecommend;
        private bool isOpen;
        private bool isPreview;

        private bool isAnimating;

        private UnityInitialize session;

        #region Public
        public bool ArtefactIsAnimating()
        {
            return isAnimating;
        }

        public bool ArtefactIsMoving() //暂时先用透明度判断
        {
            if (isAnimating)
            {
                return true;
            }
            if (isPublish)
            {
                return true;
            }
            return alpha != 1;
        }

        public bool ArtefactIsPreviewing()
        {
            return isPreview;
        }

        public bool ArtefactIsOpen()
        {
            return isOpen;
        }

        public void UpdatePreviewUrls(string[] urls)
        {
            transform.GetComponent<ArtefactPreview>().previewUrls = urls;
        }

        public void StartPreview(Boolean contentVisible)
        {
            if (!isInitial || !isRotation) return;

            if (isPublish || alpha != 1) return;

            if (isPreview || isOpen) return;

            isPreview = true;

            if (contentVisible)
            {
                StartCoroutine(PreivewAsync());
            }
            LeanTween.scale(gameObject, new Vector3(2.5f * scale, 2.5f * scale, 2.5f * scale), 0.5f).setEase(LeanTweenType.easeOutQuad);
        }
        private IEnumerator PreivewAsync()
        {
            yield return null;
            GameObject preview = transform.Find("Preview").gameObject;
            Material mat = preview.GetComponent<Renderer>().material;
            var player = preview.GetComponent<VideoPlayer>();
            yield return null;
            switch (arteType)
            {
                case Type.text:
                    mat.SetTexture("_MainTex", null);
                    player.enabled = true;
                    player.source = VideoSource.VideoClip;
                    player.clip = textPreview;
                    break;
                case Type.image:
                case Type.paint:
                case Type.mix:
                    player.enabled = false;
                    if (imagePreview != null)
                    {
                        mat.SetTexture("_MainTex", imagePreview);
                    }
                    else
                    {
                        transform.GetComponent<ArtefactPreview>().enabled = true;
                    }
                    break;
                case Type.video:
                    if (videoPreviewPath != null && videoPreviewPath.Length > 0)
                    {
                        mat.SetTexture("_MainTex", null);
                        player.enabled = true;
                        player.source = VideoSource.Url;
                        player.url = videoPreviewPath;
                        player.prepareCompleted += OnPlayerPrepared;
#if DEBUG
                        Debug.Log(videoPreviewPath);
#endif
                    }
                    else
                    {
                        player.enabled = false;
                        transform.GetComponent<ArtefactPreview>().enabled = true;
                        mat.SetTexture("_MainTex", null);
                    }
                    break;
                case Type.audio:
                    if (Application.platform == RuntimePlatform.Android) //安卓找不到路径不能播放
                    {
                        if (videoPreviewPath != null && videoPreviewPath.Length > 0)
                        {
                            mat.SetTexture("_MainTex", null);
                            player.enabled = true;
                            player.source = VideoSource.Url;
                            player.url = videoPreviewPath;
                            player.prepareCompleted += OnPlayerPrepared;
#if DEBUG
                            Debug.Log(videoPreviewPath);
#endif
                        }
                    }
                    else
                    {
                        mat.SetTexture("_MainTex", null);
                        player.enabled = true;
                        player.source = VideoSource.VideoClip;
                        player.clip = audioPreview;
                    }
                    break;
                default:
                    break;
            }
            mat.SetFloat("_Alpha", 0f);
            yield return null;
            preview.SetActive(true);
            yield return null;
            LeanTween.value(preview, 0f, 1f, 1.0f).setEase(LeanTweenType.easeOutCubic).setOnUpdate((float val) =>
            {
                mat.SetFloat("_Alpha", val);
            });

        }
        private void OnPlayerPrepared(VideoPlayer source)
        {
            //print(source.width);
            //print(source.height);
            //ArtefactPreview preview = transform.GetComponent<ArtefactPreview>();//获取到的类型不对，暂不用该方法
            //preview.AdjustScaleToFitWidthHeight(source.width, source.height);


            GameObject preview = transform.Find("Preview").gameObject;
            preview.transform.localScale = ScaleToFitWidthHeight(source.width, source.height);
        }
        public Vector3 ScaleToFitWidthHeight(float width, float height)
        {
            if (width > height)
            {
                float scale = height * 1.0f / width;
                return new Vector3(1, scale, 1) * 1.7f;
            }
            else
            {
                float scale = width * 1.0f / height;
                return new Vector3(scale, 1, 1) * 1.7f;
            }
        }
        public void StopPreview()
        {
            if (!isPreview) return;

            isPreview = false;

            GameObject preview = transform.Find("Preview").gameObject;
            Material mat = preview.GetComponent<Renderer>().material;
            VideoPlayer player = preview.GetComponent<VideoPlayer>();
            LeanTween.value(preview, mat.GetFloat("_Alpha"), 0f, 0.4f).setEase(LeanTweenType.easeInCubic).setOnUpdate((float val) =>
            {
                mat.SetFloat("_Alpha", val);
            }).setOnComplete(() =>
            {
                mat.SetTexture("_MainTex", null);
                player.clip = null;
                player.url = null;
                preview.SetActive(false);
                transform.GetComponent<ArtefactPreview>().enabled = false;
                player.enabled = false;
            });
            LeanTween.scale(gameObject, new Vector3(scale * 1.5f, scale * 1.5f, scale * 1.5f), 1.0f).setEase(LeanTweenType.easeOutQuad);
            Resources.UnloadUnusedAssets();
        }

        public void OpenDetail(Action callback)
        {
            if (isOpen || !isRotation) return;

            isOpen = true;
            isAnimating = true;

            StopPreview();

            GameObject preview = transform.Find("Preview").gameObject;
            preview.SetActive(false);

            GameObject ani = transform.Find("OpenClose").gameObject;
            ani.SetActive(true);

            GameObject hex1 = ani.transform.Find("Hexagon1").gameObject;
            GameObject hex2 = ani.transform.Find("Hexagon2").gameObject;
            GameObject trail = ani.transform.Find("Trail").gameObject;

            Material material1 = hex1.GetComponent<Renderer>().material;
            Material material2 = hex2.GetComponent<Renderer>().material;

            // scale
            material1.SetFloat("_ScaleX", 8.0f * scale);
            material1.SetFloat("_ScaleY", 8.0f * scale);

            material2.SetFloat("_ScaleX", 8.0f * scale);
            material2.SetFloat("_ScaleY", 8.0f * scale);

            LeanTween.value(hex1, 8.0f * scale, 6.0f * 8.0f * scale, 0.6f).setEase(LeanTweenType.easeOutSine).setOnUpdate((float val) =>
              {
                  material1.SetFloat("_ScaleX", val);
                  material1.SetFloat("_ScaleY", val);
              });
            LeanTween.value(hex1, 8.0f * scale, 6.5f * 8.0f * scale, 0.3f).setEase(LeanTweenType.easeOutSine).setOnUpdate((float val) =>
              {
                  material2.SetFloat("_ScaleX", val);
                  material2.SetFloat("_ScaleY", val);
              });

            // color
            Color startColor = new Color(1f, 1f, 1f, 0f);

            material1.SetColor("_Color", startColor);
            material2.SetColor("_Color", startColor);

            LeanTween.color(hex1, color, 0.3f).setEase(LeanTweenType.easeOutSine);
            LeanTween.color(hex2, new Color(1f, 1f, 1f, 0.5f), 0.2f).setEase(LeanTweenType.easeOutCirc);

            LeanTween.color(hex1, new Color(color.r, color.g, color.b, 0f), 0.3f).setEase(LeanTweenType.easeOutSine).setDelay(0.4f);
            LeanTween.color(hex2, new Color(1f, 1f, 1f, 0f), 0.1f).setEase(LeanTweenType.easeOutSine).setDelay(0.2f);

            // artefact scale
            transform.localScale = new Vector3(1.5f * scale, 1.5f * scale, 1.5f * scale);
            LeanTween.scale(gameObject, new Vector3(3.0f * scale, 3.0f * scale, 3.0f * scale), 0.2f).setEase(LeanTweenType.easeInSine);
            LeanTween.scale(gameObject, new Vector3(0.3f * scale, 0.3f * scale, 0.2f * scale), 0.4f).setEase(LeanTweenType.easeOutSine).setDelay(0.2f);

            SetAlpha(alpha);
            float lastAlpha = alpha;

            LeanTween.value(gameObject, alpha, 0f, 0.4f).setEase(LeanTweenType.easeOutSine).setDelay(0.2f).setOnUpdate(SetAlpha).setOnComplete(() =>
            {
                alpha = lastAlpha;
            });

            LeanTween.value(gameObject, 0f, -10f, 0.1f).setEase(LeanTweenType.easeOutSine).setOnUpdate((float val) =>
            {
                rotateSpeed = val;
            });

            //trail - this most likely needs to be animated in some other way in terms of positions
            //like in screenspace or realtive from the artefact in world coordinates
            trail.SetActive(true);
            trail.transform.parent = transform.root;
            trail.transform.position = transform.position;
            trail.transform.rotation = Quaternion.identity;
            trail.transform.localScale = Vector3.one;

            TrailRenderer trailRenderer = trail.GetComponent<TrailRenderer>();
            trailRenderer.startColor = color;
            trailRenderer.endColor = color;

            //指向摄像机的向量
            var direction = (mainCamera.transform.position - transform.position).normalized;

            LeanTween.moveX(trail, transform.position.x + 0.5f * direction.x, 0.3f).setDelay(0.5f).setEase(LeanTweenType.easeOutSine);
            LeanTween.moveZ(trail, transform.position.z + 0.5f * direction.z, 0.3f).setDelay(0.5f).setEase(LeanTweenType.easeOutSine);
            LeanTween.moveY(trail, transform.position.y - 0.8f, 0.3f).setDelay(0.5f).setEase(LeanTweenType.easeInSine);

            var forward = mainCamera.transform.forward.normalized;

            LeanTween.moveX(trail, mainCamera.transform.position.x + 0.25f * forward.x, 0.4f).setDelay(0.8f).setEase(LeanTweenType.easeInSine);
            LeanTween.moveZ(trail, mainCamera.transform.position.z + 0.25f * forward.z, 0.4f).setDelay(0.8f).setEase(LeanTweenType.easeInSine);
            LeanTween.moveY(trail, mainCamera.transform.position.y - 4f, 0.3f).setDelay(0.8f).setEase(LeanTweenType.easeOutSine);

            LeanTween.moveX(trail, 0f, 0.3f).setDelay(1.2f).setEase(LeanTweenType.easeInSine);
            LeanTween.moveZ(trail, 0f, 0.3f).setDelay(1.2f).setEase(LeanTweenType.easeInSine);
            LeanTween.moveY(trail, mainCamera.transform.position.y + 6f, 0.3f).setDelay(1.1f).setEase(LeanTweenType.easeInSine).setOnComplete(() =>
            {
                trail.transform.parent = ani.transform;
                ani.SetActive(false);
                isRotation = false;
                isAnimating = false;
                callback();
            });

            // dummy content
            //dummyContent.SetActive(true);
            //dummyContent.transform.localRotation = Quaternion.Euler(0f, -90f, 0f);
            //LeanTween.rotateAround(dummyContent, Vector3.up, 90f, 0.3f).setDelay(1.4f).setEase(LeanTweenType.easeOutSine);
        }

        public void CloseDetail()
        {
            if (!isOpen) return;

            isAnimating = true;

            GameObject ani = transform.Find("OpenClose").gameObject;
            ani.SetActive(true);

            GameObject hex1 = ani.transform.Find("Hexagon1").gameObject;
            GameObject hex2 = ani.transform.Find("Hexagon2").gameObject;
            GameObject trail = ani.transform.Find("Trail").gameObject;

            Material material1 = hex1.GetComponent<Renderer>().material;
            Material material2 = hex2.GetComponent<Renderer>().material;

            // trail
            trail.SetActive(true);
            trail.transform.parent = transform.root;
            trail.transform.position = new Vector3(mainCamera.transform.position.x, mainCamera.transform.position.y - 0.2f, mainCamera.transform.position.z);
            trail.transform.rotation = Quaternion.identity;
            trail.transform.localScale = Vector3.one;

            var forward = mainCamera.transform.forward.normalized;

            // 先转到正前方
            LeanTween.moveX(trail, mainCamera.transform.position.x + 1f * forward.x, 0.3f).setDelay(0.3f).setEase(LeanTweenType.easeInSine);
            LeanTween.moveZ(trail, mainCamera.transform.position.z + 1f * forward.z, 0.3f).setDelay(0.3f).setEase(LeanTweenType.easeInSine);
            LeanTween.moveY(trail, mainCamera.transform.position.y - 1f, 0.3f).setDelay(0.6f).setEase(LeanTweenType.easeOutSine);

            LeanTween.moveX(trail, mainCamera.transform.position.x + 3f * forward.x, 0.3f).setDelay(0.6f).setEase(LeanTweenType.easeOutSine);
            LeanTween.moveZ(trail, mainCamera.transform.position.z + 3f * forward.z, 0.3f).setDelay(0.6f).setEase(LeanTweenType.easeOutSine);
            LeanTween.moveY(trail, mainCamera.transform.position.y - 3f, 0.3f).setDelay(0.6f).setEase(LeanTweenType.easeInSine);

            //指向摄像机的向量
            var direction = (mainCamera.transform.position - transform.position).normalized;

            LeanTween.moveX(trail, transform.position.x + 0.3f * direction.x, 0.3f).setDelay(0.9f).setEase(LeanTweenType.easeInSine);
            LeanTween.moveZ(trail, transform.position.z + 0.3f * direction.z, 0.3f).setDelay(0.9f).setEase(LeanTweenType.easeInSine);
            LeanTween.moveY(trail, transform.position.y - 0.2f, 0.3f).setDelay(0.9f).setEase(LeanTweenType.easeOutSine);

            LeanTween.moveX(trail, transform.position.x, 0.3f).setDelay(1.2f).setEase(LeanTweenType.easeOutSine);
            LeanTween.moveZ(trail, transform.position.z, 0.3f).setDelay(1.2f).setEase(LeanTweenType.easeOutSine);
            LeanTween.moveY(trail, transform.position.y, 0.3f).setDelay(1.2f).setEase(LeanTweenType.easeInSine).setOnComplete(() =>
            {
                trail.transform.parent = ani.transform;
                trail.SetActive(false);
            });

            // scale
            material1.SetFloat("_ScaleX", 8.0f * scale);
            material1.SetFloat("_ScaleY", 8.0f * scale);

            material2.SetFloat("_ScaleX", 8.0f * scale);
            material2.SetFloat("_ScaleY", 8.0f * scale);

            LeanTween.value(hex1, 8.0f * scale, 6.0f * 8.0f * scale, 0.6f).setDelay(1.2f).setEase(LeanTweenType.easeOutSine).setOnUpdate((float val) =>
              {
                  material1.SetFloat("_ScaleX", val);
                  material1.SetFloat("_ScaleY", val);
              });
            LeanTween.value(hex1, 8.0f * scale, 6.5f * 8.0f * scale, 0.3f).setDelay(1.2f).setEase(LeanTweenType.easeOutSine).setOnUpdate((float val) =>
              {
                  material2.SetFloat("_ScaleX", val);
                  material2.SetFloat("_ScaleY", val);
              });

            // color
            Color startColor = new Color(1f, 1f, 1f, 0f);

            material1.SetColor("_Color", startColor);
            material2.SetColor("_Color", startColor);

            LeanTween.color(hex1, color, 0.3f).setDelay(1.2f).setEase(LeanTweenType.easeOutSine);
            LeanTween.color(hex2, new Color(1f, 1f, 1f, 0.5f), 0.2f).setDelay(1.2f).setEase(LeanTweenType.easeOutCirc);

            LeanTween.color(hex1, new Color(color.r, color.g, color.b, 0f), 0.3f).setDelay(1.5f).setEase(LeanTweenType.easeOutSine);
            LeanTween.color(hex2, new Color(1f, 1f, 1f, 0f), 0.1f).setEase(LeanTweenType.easeOutSine).setDelay(1.4f);

            // artefact scale
            transform.localScale = new Vector3(0.3f * scale, 0.3f * scale, 0.3f * scale);
            LeanTween.scale(gameObject, new Vector3(3f * scale, 3f * scale, 3f * scale), 0.3f).setEase(LeanTweenType.easeOutSine).setDelay(1.2f);
            LeanTween.scale(gameObject, new Vector3(1.5f * scale, 1.5f * scale, 1.5f * scale), 0.5f).setEase(LeanTweenType.easeInSine).setDelay(1.4f);

            float lastAlpha = alpha;
            SetAlpha(0f);

            LeanTween.value(gameObject, 0f, lastAlpha, 0.3f).setEase(LeanTweenType.easeInSine).setDelay(1.1f).setOnUpdate(SetAlpha).setOnComplete(() =>
            {
                alpha = lastAlpha;
            });

            LeanTween.value(gameObject, rotateSpeed, 0f, 0.4f).setEase(LeanTweenType.easeOutSine).setDelay(1.5f).setOnUpdate((float val) =>
            {
                rotateSpeed = val;
            });

            LeanTween.delayedCall(1.9f, () =>
            {
                ani.SetActive(false);
                isOpen = false;
                isRotation = true;
                isAnimating = false;
            });
        }

        //关闭不带动画
        public void Close()
        {
            if (!isOpen) return;

            SetAlpha(1f);
            isOpen = false;
            rotateSpeed = 0;
            isRotation = true;
        }

        //已关闭的先设置打开再走关闭动画
        public void CloseAnimation()
        {
            SetAlpha(0);
            isOpen = true;
            rotateSpeed = -10f;
            isRotation = false;
            CloseDetail();
        }

        //打开问题
        public void OpenAnswer(Action callback)
        {
            if (isOpen || !isRotation) return;

            isOpen = true;
            isAnimating = true;

            GameObject ani = transform.Find("OpenClose").gameObject;
            ani.SetActive(true);

            GameObject hex1 = ani.transform.Find("Hexagon1").gameObject;
            GameObject hex2 = ani.transform.Find("Hexagon2").gameObject;
            GameObject trail = ani.transform.Find("Trail").gameObject;

            Material material1 = hex1.GetComponent<Renderer>().material;
            Material material2 = hex2.GetComponent<Renderer>().material;

            // scale
            material1.SetFloat("_ScaleX", 1.0f);
            material1.SetFloat("_ScaleY", 1.0f);

            material2.SetFloat("_ScaleX", 1.0f);
            material2.SetFloat("_ScaleY", 1.0f);

            LeanTween.value(hex1, 1.0f, 6.0f, 0.6f).setEase(LeanTweenType.easeOutSine).setOnUpdate((float val) =>
            {
                material1.SetFloat("_ScaleX", val);
                material1.SetFloat("_ScaleY", val);
            });
            LeanTween.value(hex1, 1.0f, 6.5f, 0.3f).setEase(LeanTweenType.easeOutSine).setOnUpdate((float val) =>
            {
                material2.SetFloat("_ScaleX", val);
                material2.SetFloat("_ScaleY", val);
            });

            // color
            Color startColor = new Color(1f, 1f, 1f, 0f);

            material1.SetColor("_Color", startColor);
            material2.SetColor("_Color", startColor);

            LeanTween.color(hex1, color, 0.3f).setEase(LeanTweenType.easeOutSine);
            LeanTween.color(hex2, new Color(1f, 1f, 1f, 0.5f), 0.2f).setEase(LeanTweenType.easeOutCirc);

            LeanTween.color(hex1, new Color(color.r, color.g, color.b, 0f), 0.3f).setEase(LeanTweenType.easeOutSine).setDelay(0.4f);
            LeanTween.color(hex2, new Color(1f, 1f, 1f, 0f), 0.1f).setEase(LeanTweenType.easeOutSine).setDelay(0.2f);

            // artefact scale
            transform.localScale = new Vector3(1.5f * scale, 1.5f * scale, 1.5f * scale);
            LeanTween.scale(gameObject, new Vector3(3.0f * scale, 3.0f * scale, 3.0f * scale), 0.2f).setEase(LeanTweenType.easeInSine);
            LeanTween.scale(gameObject, new Vector3(0.3f * scale, 0.3f * scale, 0.2f * scale), 0.4f).setEase(LeanTweenType.easeOutSine).setDelay(0.2f);

            SetAlpha(alpha);
            float lastAlpha = alpha;

            LeanTween.value(gameObject, alpha, 0f, 0.4f).setEase(LeanTweenType.easeOutSine).setDelay(0.2f).setOnUpdate(SetAlpha).setOnComplete(() =>
            {
                alpha = lastAlpha;
            });

            LeanTween.value(gameObject, 0f, -10f, 0.1f).setEase(LeanTweenType.easeOutSine).setOnUpdate((float val) =>
            {
                rotateSpeed = val;
            });

            //trail - this most likely needs to be animated in some other way in terms of positions
            //like in screenspace or realtive from the artefact in world coordinates
            trail.SetActive(true);
            trail.transform.parent = transform.root;
            trail.transform.position = transform.position;
            trail.transform.rotation = Quaternion.identity;
            trail.transform.localScale = Vector3.one;

            TrailRenderer trailRenderer = trail.GetComponent<TrailRenderer>();
            trailRenderer.startColor = color;
            trailRenderer.endColor = color;

            //指向摄像机的向量
            var direction = (mainCamera.transform.position - transform.position).normalized;

            LeanTween.moveX(trail, transform.position.x + 0.5f * direction.x, 0.3f).setDelay(0.5f).setEase(LeanTweenType.easeOutSine);
            LeanTween.moveZ(trail, transform.position.z + 0.5f * direction.z, 0.3f).setDelay(0.5f).setEase(LeanTweenType.easeOutSine);
            LeanTween.moveY(trail, transform.position.y - 0.8f, 0.3f).setDelay(0.5f).setEase(LeanTweenType.easeInSine);

            var forward = mainCamera.transform.forward.normalized;

            LeanTween.moveX(trail, mainCamera.transform.position.x + 0.25f * forward.x, 0.4f).setDelay(0.8f).setEase(LeanTweenType.easeInSine);
            LeanTween.moveZ(trail, mainCamera.transform.position.z + 0.25f * forward.z, 0.4f).setDelay(0.8f).setEase(LeanTweenType.easeInSine);
            LeanTween.moveY(trail, mainCamera.transform.position.y - 4f, 0.3f).setDelay(0.8f).setEase(LeanTweenType.easeOutSine);

            LeanTween.moveX(trail, 0f, 0.3f).setDelay(1.2f).setEase(LeanTweenType.easeInSine);
            LeanTween.moveZ(trail, 0f, 0.3f).setDelay(1.2f).setEase(LeanTweenType.easeInSine);
            LeanTween.moveY(trail, mainCamera.transform.position.y + 6f, 0.3f).setDelay(1.1f).setEase(LeanTweenType.easeInSine).setOnComplete(() =>
            {
                trail.transform.parent = ani.transform;
                ani.SetActive(false);
                isRotation = false;
                isAnimating = false;
                callback();
            });
        }

        public void CloseAnswer()
        {
            if (!isOpen) return;

            isAnimating = true;

            // artefact scale
            LeanTween.scale(gameObject, new Vector3(1.5f * scale, 1.5f * scale, 1.5f * scale), 0.5f).setEase(LeanTweenType.easeInSine);

            float lastAlpha = alpha;
            SetAlpha(0f);

            LeanTween.value(gameObject, 0f, lastAlpha, 0.5f).setEase(LeanTweenType.easeInSine).setOnUpdate(SetAlpha).setOnComplete(() =>
            {
                alpha = lastAlpha;
                isOpen = false;
                isRotation = true;
                isAnimating = false;
            });

            LeanTween.value(gameObject, rotateSpeed, 0f, 0.4f).setEase(LeanTweenType.easeOutSine).setDelay(0.4f).setOnUpdate((float val) =>
            {
                rotateSpeed = val;
            });
        }

        public void Publish()
        {
            isPublish = true;
            isAnimating = true;

            var forward = mainCamera.transform.forward.normalized;
            var down = -mainCamera.transform.up.normalized;

            var targetPosition = mainCamera.transform.position + 2f * forward;
            Warp().initialPosition = targetPosition;
            Warp().transform.position = targetPosition;

            GameObject ani = transform.Find("OpenClose").gameObject;
            ani.SetActive(true);

            GameObject hex1 = ani.transform.Find("Hexagon1").gameObject;
            GameObject hex2 = ani.transform.Find("Hexagon2").gameObject;
            GameObject trail = ani.transform.Find("Trail").gameObject;

            Material material1 = hex1.GetComponent<Renderer>().material;
            Material material2 = hex2.GetComponent<Renderer>().material;

            // trail
            trail.SetActive(true);
            trail.transform.parent = transform.root;
            trail.transform.position = mainCamera.transform.position;
            trail.transform.rotation = Quaternion.identity;
            trail.transform.localScale = Vector3.one;

            TrailRenderer trailRenderer = trail.GetComponent<TrailRenderer>();
            trailRenderer.startColor = color;
            trailRenderer.endColor = color;

            var controlPoint = mainCamera.transform.position + 3f * forward;

            var downPoint = controlPoint + 4f * down;

            //直接移动路线不是弧形而是直线，分别移动动画曲线不用才是弧线
            //LeanTween.move(trail, new Vector3(x: mainCamera.transform.position.x + 1f * forward.x, y: mainCamera.transform.position.y - 4f, z: mainCamera.transform.position.z + 1f * forward.z), 0.3f).setDelay(0.3f).setEase(LeanTweenType.easeInSine);
            LeanTween.moveX(trail, downPoint.x, 0.3f).setDelay(0.3f).setEase(LeanTweenType.easeInSine);
            LeanTween.moveZ(trail, downPoint.z, 0.3f).setDelay(0.3f).setEase(LeanTweenType.easeInSine);
            LeanTween.moveY(trail, downPoint.y, 0.3f).setDelay(0.3f).setEase(LeanTweenType.easeOutSine);

            LeanTween.moveX(trail, transform.position.x, 0.3f).setDelay(0.6f).setEase(LeanTweenType.easeOutSine);
            LeanTween.moveZ(trail, transform.position.z, 0.3f).setDelay(0.6f).setEase(LeanTweenType.easeOutSine);
            LeanTween.moveY(trail, transform.position.y, 0.3f).setDelay(0.6f).setEase(LeanTweenType.easeInSine).setOnComplete(() =>
            {
                trail.transform.parent = ani.transform;
                trail.SetActive(false);
            });

            // scale
            material1.SetFloat("_ScaleX", 1.0f);
            material1.SetFloat("_ScaleY", 1.0f);

            material2.SetFloat("_ScaleX", 1.0f);
            material2.SetFloat("_ScaleY", 1.0f);

            LeanTween.value(hex1, 1.0f, 6.0f, 0.6f).setDelay(0.9f).setEase(LeanTweenType.easeOutSine).setOnUpdate((float val) =>
            {
                material1.SetFloat("_ScaleX", val);
                material1.SetFloat("_ScaleY", val);
            });
            LeanTween.value(hex1, 1.0f, 6.5f, 0.3f).setDelay(0.9f).setEase(LeanTweenType.easeOutSine).setOnUpdate((float val) =>
            {
                material2.SetFloat("_ScaleX", val);
                material2.SetFloat("_ScaleY", val);
            });

            // color
            Color startColor = new Color(1f, 1f, 1f, 0f);

            material1.SetColor("_Color", startColor);
            material2.SetColor("_Color", startColor);

            LeanTween.color(hex1, color, 0.3f).setDelay(0.9f).setEase(LeanTweenType.easeOutSine);
            LeanTween.color(hex2, new Color(1f, 1f, 1f, 0.5f), 0.2f).setDelay(0.9f).setEase(LeanTweenType.easeOutCirc);

            LeanTween.color(hex1, new Color(color.r, color.g, color.b, 0f), 0.3f).setDelay(1.2f).setEase(LeanTweenType.easeOutSine);
            LeanTween.color(hex2, new Color(1f, 1f, 1f, 0f), 0.1f).setEase(LeanTweenType.easeOutSine).setDelay(1.4f);

            // artefact scale
            transform.localScale = new Vector3(0.2f * scale, 0.2f * scale, 0.2f * scale);
            LeanTween.scale(gameObject, new Vector3(2f * scale, 2f * scale, 2f * scale), 0.3f).setEase(LeanTweenType.easeOutSine).setDelay(1.2f);
            LeanTween.scale(gameObject, new Vector3(1.5f * scale, 1.5f * scale, 1.5f * scale), 0.5f).setEase(LeanTweenType.easeInSine).setDelay(1.4f);

            SetAlpha(0f);

            LeanTween.value(gameObject, 0f, 1f, 0.3f).setEase(LeanTweenType.easeInSine).setDelay(1.1f).setOnUpdate(SetAlpha).setOnComplete(() =>
            {
                alpha = 1.0f;
            }); ;

            LeanTween.value(gameObject, rotateSpeed, 0f, 0.4f).setEase(LeanTweenType.easeOutSine).setDelay(1.5f).setOnUpdate((float val) =>
            {
                rotateSpeed = val;
            });

            LeanTween.delayedCall(3.0f, () =>
            {
                ani.SetActive(false);
                isPublish = false;
                isAnimating = false;
                Warp().published = true;
            });
        }

        public void Recommond()
        {
            if (isRecommend || isPublish)
            {
                return;
            }

            isPublish = true;
            isAnimating = true;

            GameObject ani = transform.Find("OpenClose").gameObject;

            GameObject hex1 = ani.transform.Find("Hexagon1").gameObject;
            GameObject hex2 = ani.transform.Find("Hexagon2").gameObject;
            GameObject trail = ani.transform.Find("Trail").gameObject;

            Material material1 = hex1.GetComponent<Renderer>().material;
            Material material2 = hex2.GetComponent<Renderer>().material;

            // scale
            material1.SetFloat("_ScaleX", 1.0f);
            material1.SetFloat("_ScaleY", 1.0f);

            material2.SetFloat("_ScaleX", 1.0f);
            material2.SetFloat("_ScaleY", 1.0f);

            var delay = Random.Range(1.2f, 2f);

            LeanTween.delayedCall(1.2f, () =>
             {
                 ani.SetActive(true);
             });

            if (GameObject.Find("AR Session").GetComponent<ARSession>().isActiveAndEnabled)
            {
                LeanTween.delayedCall(delay, () =>
                {
                    AudioManager.Instance.PlayAudioClip("recommend");
                });
            }

            LeanTween.value(hex1, 1.0f, 6.0f, 0.6f).setDelay(delay).setEase(LeanTweenType.easeOutSine).setOnUpdate((float val) =>
            {
                material1.SetFloat("_ScaleX", val);
                material1.SetFloat("_ScaleY", val);
            });
            LeanTween.value(hex1, 1.0f, 6.5f, 0.3f).setDelay(delay).setEase(LeanTweenType.easeOutSine).setOnUpdate((float val) =>
            {
                material2.SetFloat("_ScaleX", val);
                material2.SetFloat("_ScaleY", val);
            });

            // color
            Color startColor = new Color(1f, 1f, 1f, 0f);

            material1.SetColor("_Color", startColor);
            material2.SetColor("_Color", startColor);

            LeanTween.color(hex1, color, 0.3f).setDelay(delay).setEase(LeanTweenType.easeOutSine);
            LeanTween.color(hex2, new Color(1f, 1f, 1f, 0.5f), 0.2f).setDelay(delay).setEase(LeanTweenType.easeOutCirc);

            LeanTween.color(hex1, new Color(color.r, color.g, color.b, 0f), 0.3f).setDelay(delay + 0.3f).setEase(LeanTweenType.easeOutSine);
            LeanTween.color(hex2, new Color(1f, 1f, 1f, 0f), 0.1f).setEase(LeanTweenType.easeOutSine).setDelay(delay + 0.5f);

            // artefact scale
            transform.localScale = new Vector3(0.2f * scale, 0.2f * scale, 0.2f * scale);
            LeanTween.scale(gameObject, new Vector3(2f * scale, 2f * scale, 2f * scale), 0.3f).setEase(LeanTweenType.easeOutSine).setDelay(delay + 0.3f);
            LeanTween.scale(gameObject, new Vector3(1.5f * scale, 1.5f * scale, 1.5f * scale), 0.5f).setEase(LeanTweenType.easeInSine).setDelay(delay + 0.5f);

            SetAlpha(0f);

            LeanTween.value(gameObject, 0f, 1f, 0.3f).setEase(LeanTweenType.easeInSine).setDelay(delay + 0.2f).setOnUpdate(SetAlpha);

            LeanTween.value(gameObject, rotateSpeed, 0f, 0.4f).setEase(LeanTweenType.easeOutSine).setDelay(delay + 0.6f).setOnUpdate((float val) =>
             {
                 alpha = 1.0f;
                 rotateSpeed = val;
             });

            LeanTween.delayedCall(delay + 1f, () =>
              {
                  ani.SetActive(false);
                  isPublish = false;
                  isAnimating = false;
                  isRecommend = true;
                  Warp().published = true;
              });
        }

        public void Dissolve(Action callback)
        {

            isRotation = false;
            isAnimating = true;

            GameObject preview = transform.Find("Preview").gameObject;
            preview.SetActive(false);

            //mask.SetActive(true);
            mask.transform.localPosition = new Vector3(4f, -1f, 0f);

            LeanTween.moveX(mask, 0f, 0.5f);
            LeanTween.moveY(mask, 0f, 0.5f);

            dissolve.SetActive(true);
            //dissolve.transform.localPosition = new Vector3(1.75f, 0f, 0f);
            //LeanTween.moveX(dissolve, -1.75f, 0.5f);
            //dissolve.transform.localPosition = new Vector3(0f, 0.2f, 0f);
            //dissolve.transform.eulerAngles = new Vector3(0, mainCamera.transform.eulerAngles.y + 90, 0);

            ParticleSystem ps = dissolve.transform.GetComponent<ParticleSystem>();
            var emission = ps.emission;
            emission.rateOverTime = 2000f;

            var main = ps.main;

            Color lighter = Color.Lerp(color, Color.white, 0.1f);
            main.startColor = new ParticleSystem.MinMaxGradient(lighter, lighter);

            ParticleSystem.ShapeModule shapeModule = ps.shape;
            shapeModule.radius = 0.2f * scale;
            LeanTween.value(mask, shapeModule.radius, 1.3f * scale, 0.25f).setEase(LeanTweenType.easeOutCirc).setOnUpdate((float val) =>
            {
                shapeModule.radius = val;
            });
            LeanTween.value(mask, 1.3f * scale, 0.2f * scale, 0.25f).setEase(LeanTweenType.easeInCirc).setDelay(0.25f).setOnUpdate((float val) =>
            {
                shapeModule.radius = val;
            }).setOnComplete(() =>
            {
                emission.rateOverTime = 0f;
            });

            GameObject outer = transform.Find("OuterGlow").gameObject;
            Color glowColor = new Color(color.r, color.g, color.b, alpha * glow);
            LeanTween.color(outer, new Color(color.r, color.g, color.b, 0f), 0.5f);

            GameObject dissolveGlow = transform.Find("DissolveGlow").gameObject;
            dissolveGlow.SetActive(true);

            Material dissolveMaterial = dissolveGlow.GetComponent<Renderer>().material;
            Material fade1Material = dissolveGlow.transform.Find("fade1").GetComponent<Renderer>().material;
            Material fade2Material = dissolveGlow.transform.Find("fade2").GetComponent<Renderer>().material;

            fade1Material.SetColor("_Color", Color.white);
            fade2Material.SetColor("_Color", color);

            dissolveMaterial.SetFloat("_ScaleX", 0.5f);
            dissolveMaterial.SetFloat("_ScaleY", 0.5f);
            fade1Material.SetFloat("_ScaleX", 0.7f);
            fade1Material.SetFloat("_ScaleY", 0.7f);
            fade2Material.SetFloat("_ScaleX", 1.5f);
            fade2Material.SetFloat("_ScaleY", 1.5f);

            LeanTween.value(dissolve, 0.5f, 0.8f, 0.6f).setEase(LeanTweenType.easeInSine).setOnUpdate((float val) =>
            {
                dissolveMaterial.SetFloat("_ScaleX", val);
                dissolveMaterial.SetFloat("_ScaleY", val);
            });

            LeanTween.value(dissolve, 0.8f, 0f, 0.5f).setEase(LeanTweenType.easeInCirc).setDelay(0.6f).setOnUpdate((float val) =>
            {
                dissolveMaterial.SetFloat("_ScaleX", val);
                dissolveMaterial.SetFloat("_ScaleY", val);
            });

            LeanTween.value(dissolve, 0.7f, 1.5f, 1f).setEase(LeanTweenType.easeOutQuad).setDelay(0.7f).setOnUpdate((float val) =>
            {
                fade1Material.SetFloat("_ScaleX", val);
                fade1Material.SetFloat("_ScaleY", val);
            });

            LeanTween.value(dissolve, 1.5f, 4f, 1f).setEase(LeanTweenType.easeOutQuad).setDelay(0.7f).setOnUpdate((float val) =>
            {
                fade2Material.SetFloat("_ScaleX", val);
                fade2Material.SetFloat("_ScaleY", val);
            });

            LeanTween.value(dissolve, alpha, 0f, 1f).setEase(LeanTweenType.easeOutQuad).setOnUpdate((float val) =>
            {
                SetAlpha(val);
            });

            LeanTween.color(dissolveGlow.transform.Find("fade1").gameObject, new Color(1f, 1f, 1f, 0f), 0.9f).setEase(LeanTweenType.easeOutQuad).setDelay(0.8f);
            LeanTween.color(dissolveGlow.transform.Find("fade2").gameObject, new Color(color.r, color.g, color.b, 0f), 0.9f).setEase(LeanTweenType.easeOutQuad).setDelay(0.8f);


            LeanTween.delayedCall(2f, () =>
            {
                Destroy(gameObject);
                callback();
            });

        }

        public void CloseDissolve(Action callback)
        {
            if (!isOpen) return;

            isAnimating = true;

            GameObject ani = transform.Find("OpenClose").gameObject;
            ani.SetActive(true);

            GameObject hex1 = ani.transform.Find("Hexagon1").gameObject;
            GameObject hex2 = ani.transform.Find("Hexagon2").gameObject;
            GameObject trail = ani.transform.Find("Trail").gameObject;

            Material material1 = hex1.GetComponent<Renderer>().material;
            Material material2 = hex2.GetComponent<Renderer>().material;

            // trail
            trail.SetActive(true);
            trail.transform.parent = transform.root;
            trail.transform.position = new Vector3(mainCamera.transform.position.x, mainCamera.transform.position.y - 0.2f, mainCamera.transform.position.z);
            trail.transform.rotation = Quaternion.identity;
            trail.transform.localScale = Vector3.one;

            TrailRenderer trailRenderer = trail.GetComponent<TrailRenderer>();
            trailRenderer.startColor = color;
            trailRenderer.endColor = color;

            var forward = mainCamera.transform.forward.normalized;

            // 先转到正前方
            LeanTween.moveX(trail, mainCamera.transform.position.x + 1f * forward.x, 0.3f).setDelay(0.3f).setEase(LeanTweenType.easeInSine);
            LeanTween.moveZ(trail, mainCamera.transform.position.z + 1f * forward.z, 0.3f).setDelay(0.3f).setEase(LeanTweenType.easeInSine);
            LeanTween.moveY(trail, mainCamera.transform.position.y - 1f, 0.3f).setDelay(0.6f).setEase(LeanTweenType.easeOutSine);

            LeanTween.moveX(trail, mainCamera.transform.position.x + 3f * forward.x, 0.3f).setDelay(0.6f).setEase(LeanTweenType.easeOutSine);
            LeanTween.moveZ(trail, mainCamera.transform.position.z + 3f * forward.z, 0.3f).setDelay(0.6f).setEase(LeanTweenType.easeOutSine);
            LeanTween.moveY(trail, mainCamera.transform.position.y - 3f, 0.3f).setDelay(0.6f).setEase(LeanTweenType.easeInSine);

            //指向摄像机的向量
            var direction = (mainCamera.transform.position - transform.position).normalized;

            LeanTween.moveX(trail, transform.position.x + 0.3f * direction.x, 0.3f).setDelay(0.9f).setEase(LeanTweenType.easeInSine);
            LeanTween.moveZ(trail, transform.position.z + 0.3f * direction.z, 0.3f).setDelay(0.9f).setEase(LeanTweenType.easeInSine);
            LeanTween.moveY(trail, transform.position.y - 0.2f, 0.3f).setDelay(0.9f).setEase(LeanTweenType.easeOutSine);

            LeanTween.moveX(trail, transform.position.x, 0.3f).setDelay(1.2f).setEase(LeanTweenType.easeOutSine);
            LeanTween.moveZ(trail, transform.position.z, 0.3f).setDelay(1.2f).setEase(LeanTweenType.easeOutSine);
            LeanTween.moveY(trail, transform.position.y, 0.3f).setDelay(1.2f).setEase(LeanTweenType.easeInSine).setOnComplete(() =>
            {
                trail.transform.parent = ani.transform;
                trail.SetActive(false);
            });

            // scale
            material1.SetFloat("_ScaleX", 8.0f * scale);
            material1.SetFloat("_ScaleY", 8.0f * scale);

            material2.SetFloat("_ScaleX", 8.0f * scale);
            material2.SetFloat("_ScaleY", 8.0f * scale);

            LeanTween.value(hex1, 8.0f * scale, 6.0f * 8.0f * scale, 0.6f).setDelay(1.2f).setEase(LeanTweenType.easeOutSine).setOnUpdate((float val) =>
              {
                  material1.SetFloat("_ScaleX", val);
                  material1.SetFloat("_ScaleY", val);
              });
            LeanTween.value(hex1, 8.0f * scale, 6.5f * 8.0f * scale, 0.3f).setDelay(1.2f).setEase(LeanTweenType.easeOutSine).setOnUpdate((float val) =>
              {
                  material2.SetFloat("_ScaleX", val);
                  material2.SetFloat("_ScaleY", val);
              });

            // color
            Color startColor = new Color(1f, 1f, 1f, 0f);

            material1.SetColor("_Color", startColor);
            material2.SetColor("_Color", startColor);

            LeanTween.color(hex1, color, 0.3f).setDelay(1.2f).setEase(LeanTweenType.easeOutSine);
            LeanTween.color(hex2, new Color(1f, 1f, 1f, 0.5f), 0.2f).setDelay(1.2f).setEase(LeanTweenType.easeOutCirc);

            LeanTween.color(hex1, new Color(color.r, color.g, color.b, 0f), 0.3f).setDelay(1.5f).setEase(LeanTweenType.easeOutSine);
            LeanTween.color(hex2, new Color(1f, 1f, 1f, 0f), 0.1f).setEase(LeanTweenType.easeOutSine).setDelay(1.4f);

            // artefact scale
            transform.localScale = new Vector3(0.3f * scale, 0.3f * scale, 0.3f * scale);
            LeanTween.scale(gameObject, new Vector3(3f * scale, 3f * scale, 3f * scale), 0.3f).setEase(LeanTweenType.easeOutSine).setDelay(1.2f);
            LeanTween.scale(gameObject, new Vector3(1.5f * scale, 1.5f * scale, 1.5f * scale), 0.5f).setEase(LeanTweenType.easeInSine).setDelay(1.4f);

            float lastAlpha = alpha;
            SetAlpha(0f);

            LeanTween.value(gameObject, 0f, lastAlpha, 0.3f).setEase(LeanTweenType.easeInSine).setDelay(1.1f).setOnUpdate(SetAlpha).setOnComplete(() =>
            {
                alpha = lastAlpha;
            });

            LeanTween.value(gameObject, rotateSpeed, 0f, 0.4f).setEase(LeanTweenType.easeOutSine).setDelay(1.5f).setOnUpdate((float val) =>
            {
                rotateSpeed = val;
            });

            LeanTween.delayedCall(2f, () =>
            {
                Dissolve(callback);
            });
        }

        public void AnimatedDestory()
        {
            isAnimating = true;

            StopPreview();
            LeanTween.value(transform.gameObject, alpha, 0.0f, 2.0f).setEase(LeanTweenType.easeOutCubic).setOnUpdate((float val) =>
            {
                SetAlpha(val);
            }).setOnComplete(() =>
            {
                Destroy(Warp().gameObject);
            });
        }

        // 删除带动画
        public void Vanish(Action callback)
        {
            isAnimating = true;

            GameObject ani = transform.Find("OpenClose").gameObject;
            GameObject trail = ani.transform.Find("Trail").gameObject;

            // trail
            trail.SetActive(true);
            trail.transform.parent = transform.root;
            trail.transform.position = mainCamera.transform.position;
            trail.transform.rotation = Quaternion.identity;
            trail.transform.localScale = Vector3.one;

            TrailRenderer trailRenderer = trail.GetComponent<TrailRenderer>();
            trailRenderer.startColor = color;
            trailRenderer.endColor = color;

            var cameraPos = mainCamera.transform.position;
            var forward = mainCamera.transform.forward.normalized;

            LeanTween.moveX(trail, cameraPos.x + 3f * forward.x, 0.3f).setDelay(0.3f).setEase(LeanTweenType.easeInSine);
            LeanTween.moveZ(trail, cameraPos.z + 3f * forward.z, 0.3f).setDelay(0.3f).setEase(LeanTweenType.easeInSine);
            LeanTween.moveY(trail, cameraPos.y - 4f, 0.3f).setDelay(0.3f).setEase(LeanTweenType.easeOutSine);

            var point1 = new Vector3(cameraPos.x + 6f * forward.x, cameraPos.y, cameraPos.z + 6f * forward.z);
            var point2 = new Vector3(cameraPos.x + 10f * forward.x, cameraPos.y, cameraPos.z + 10f * forward.z);

            //指向拐弯点的向量
            var direction = (cameraPos - point2).normalized;
            var vertical = new Vector3(0, -1f, 0);
            var cross = Vector3.Cross(direction, vertical);

            //拐弯点
            var roundPos = new Vector3(point1.x + 1f * cross.x, point1.y - 1f, point1.z + 1f * cross.z);
            //消失点向量
            var vanishPos = new Vector3(point2.x + 200f * cross.x, point2.y, point2.z + 200f * cross.z);

            LeanTween.moveX(trail, roundPos.x, 0.3f).setDelay(0.6f).setEase(LeanTweenType.easeOutSine);
            LeanTween.moveZ(trail, roundPos.z, 0.3f).setDelay(0.6f).setEase(LeanTweenType.easeOutSine);
            LeanTween.moveY(trail, roundPos.y, 0.3f).setDelay(0.6f).setEase(LeanTweenType.easeInSine);

            LeanTween.moveX(trail, vanishPos.x, 1.2f).setDelay(0.9f).setEase(LeanTweenType.easeInSine);
            LeanTween.moveZ(trail, vanishPos.z, 1.2f).setDelay(0.9f).setEase(LeanTweenType.easeInSine);
            LeanTween.moveY(trail, vanishPos.y, 1.2f).setDelay(0.9f).setEase(LeanTweenType.easeOutSine).setOnComplete(() =>
            {
                trail.transform.parent = ani.transform;
                trail.SetActive(false);
            });

            LeanTween.delayedCall(2.2f, () =>
            {
                callback();
            });
        }

        public ArtefactWarp Warp()
        {
            return transform.GetComponentInParent<ArtefactWarp>();
        }
        #endregion

        #region System
        private void Awake()
        {
            Layer = LayerMask.NameToLayer("Artefact");

            session = GameObject.Find("AR Session Origin").GetComponent<UnityInitialize>();
        }

        private void Start()
        {
            initialPos = Vector3.zero;
            mainCamera = Camera.main;
            LeanTween.delayedCall(2f, () =>
            {
                isInitial = true;
                transform.Find("Particles").gameObject.SetActive(true);
            });
        }

        private void Update()
        {
            deltaTime += Time.deltaTime;

            if (isRotation && session.sessionActive)
            {
                transform.localPosition = new Vector3(0, initialPos.y + Mathf.Sin(deltaTime * 0.5f) * 1f * scale, 0);

                var ran = Random.Range(1.2f, 2f);
                transform.Rotate(Mathf.Sin(deltaTime * 0.1f + ran) * 0.5f, Mathf.Sin(deltaTime * 0.2f + ran) * 0.4f + rotateSpeed, Mathf.Cos(deltaTime * 0.1f + ran) * 0.3f, Space.Self);
            }
        }
        #endregion

        #region Private
        private void SetGeometry(ArteGeometry g)
        {
            rawData = rawDatas[g.GetHashCode()];

            GetComponent<MeshFilter>().mesh = rawData.mesh;

            GameObject hex1 = transform.Find("OpenClose").Find("Hexagon1").gameObject;
            GameObject hex2 = transform.Find("OpenClose").Find("Hexagon2").gameObject;

            Material material1 = hex1.GetComponent<Renderer>().material;
            Material material2 = hex2.GetComponent<Renderer>().material;

            material1.SetTexture("_MainTex", rawData.outline);
            material2.SetTexture("_MainTex", rawData.fill);
        }

        private void SetAlpha(float a)
        {

            GetComponent<Renderer>().material.SetFloat("_Alpha", a);

            Color tempColor = transform.Find("InnerGlow").GetComponent<Renderer>().material.color;
            tempColor.a = 0.6f * a;
            transform.Find("InnerGlow").GetComponent<Renderer>().material.SetColor("_Color", tempColor);

            tempColor = transform.Find("OuterGlow").GetComponent<Renderer>().material.color;
            tempColor.a = a * glow;
            transform.Find("OuterGlow").GetComponent<Renderer>().material.SetColor("_Color", tempColor);

            tempColor = transform.Find("InnerPulse").GetComponent<Renderer>().material.color;
            tempColor.a = 0.7f * a;
            transform.Find("InnerPulse").GetComponent<Renderer>().material.SetColor("_Color", tempColor);

            transform.Find("Particles").localScale = new Vector3(a, a, a);

        }

        private void SetGlow(float g)
        {

            Color temp = transform.Find("OuterGlow").GetComponent<Renderer>().material.color;
            temp.a = g * alpha;
            transform.Find("OuterGlow").GetComponent<Renderer>().material.SetColor("_Color", temp);

        }

        private void SetScale(float s)
        {
            var vector = new Vector3(x: s * 1.5f, y: s * 1.5f, z: s * 1.5f);
            transform.localScale = vector;

            ArtePulse innerGlow = transform.Find("InnerGlow").GetComponent<ArtePulse>();
            innerGlow.baseScale = 0.5f * s;
            innerGlow.maxScale = 1.2f * s;

            ArtePulse innerPulse = transform.Find("InnerPulse").GetComponent<ArtePulse>();
            innerPulse.baseScale = 1.2f * s;
            innerPulse.maxScale = 3.5f * s;

            ArtePulse outerGlow = transform.Find("OuterGlow").GetComponent<ArtePulse>();
            outerGlow.baseScale = 4f * s * 0.8f;
            outerGlow.maxScale = 4.5f * s * 0.8f;

            ParticleSystem particleSystem = transform.Find("Particles").GetComponent<ParticleSystem>();

            var main = particleSystem.main;
            main.startSize = new ParticleSystem.MinMaxCurve(0.5f * s, 1.25f * s);

            ParticleSystem.ShapeModule shape = particleSystem.shape;
            shape.radius = s;

            ParticleSystemRenderer render = transform.Find("Particles").GetComponent<ParticleSystemRenderer>();
            render.maxParticleSize = 0.5f * s;
        }

        private void SetColor(Color c)
        {
            GetComponent<Renderer>().material.SetColor("_RimColor", c);

            Color glowColor = new Color(c.r, c.g, c.b, alpha * glow);
            transform.Find("OuterGlow").GetComponent<Renderer>().material.SetColor("_Color", glowColor);

            Color darker = Color.Lerp(c, Color.black, 0.5f);
            darker.a = alpha;
            transform.Find("InnerPulse").GetComponent<Renderer>().material.SetColor("_Color", darker);
        }
        #endregion

    }
}
