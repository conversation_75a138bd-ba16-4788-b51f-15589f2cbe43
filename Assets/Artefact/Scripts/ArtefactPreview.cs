using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;

using UnityEngine;
using UnityEngine.Networking;

using UnityImageLoader;

namespace Com.SceneConsole.Arte
{
    public class ArtefactPreview : MonoBehaviour
    {

        /// <summary>
        /// 预览图
        /// </summary>
        [HideInInspector]
        public string[] previewUrls;

        /// <summary>
        /// 图片预览
        /// </summary>
        private List<string> keys;
        private List<Texture2D> values;

        /// <summary>
        /// 预览材质
        /// </summary>
        private Material mat;
        private GameObject preview;
        private float originScale = 1.7f;

        private float _time;
        private int _index = -1;

        // Start is called before the first frame update
        void Start()
        {
            preview = transform.Find("Preview").gameObject;
            mat = preview.GetComponent<Renderer>().material;
            originScale = preview.transform.localScale.x;
        }

        void Update()
        {
            if (values == null)
            {
                return;
            }
            if (values.Count == 0)
            {
                mat.SetTexture("_MainTex", null);
                return;
            }
            if (values.Count == 1)
            {
                mat.SetTexture("_MainTex", values[0]);
                AdjustScaleToFitWidthHeight(values[0].width, values[0].height);
                return;
            }
            float time = Time.time;
            if (time < _time)
            {
                return;
            }
            else
            {
                _time = time + 0.2f;
            }

            _index += 1;
            if (_index >= values.Count)
            {
                _index = 0;
            }
            StartCoroutine(PreviewAsync());
        }

        public void AdjustScaleToFitWidthHeight(float width, float height)
        {
            if (width > height)
            {
                float scale = height * 1.0f / width;
                preview.transform.localScale = new Vector3(1, scale, 1) * originScale;
            }
            else
            {
                float scale = width * 1.0f / height;
                preview.transform.localScale = new Vector3(scale, 1, 1) * originScale;
            }
        }

        private IEnumerator PreviewAsync()
        {
            yield return new WaitForEndOfFrame();

            var tex = values[_index];
            mat.SetTexture("_MainTex", tex);
            AdjustScaleToFitWidthHeight(tex.width, tex.height);
        }

        private void OnEnable()
        {
            if (previewUrls != null)
            {
                if (keys == null)
                {
                    keys = new List<string>();
                }
                if (values == null)
                {
                    values = new List<Texture2D>();
                }
                foreach (var item in previewUrls)
                {
                    if (!keys.Contains(item))
                    {
                        keys.Add(item);
                        LoadPreview(item);
                    }
                }
            }
        }

        private void OnDisable()
        {
            mat.SetTexture("_MainTex", null);
            keys.Clear();
            foreach (var item in values)
            {
                DestroyImmediate(item);
            }
            values.Clear();
        }

        private void OnDestroy()
        {
            mat.SetTexture("_MainTex", null);
            if (keys != null)
            {
                keys.Clear();
            }
            if (values != null)
            {
                foreach (var item in values)
                {
                    DestroyImmediate(item);
                }
                values.Clear();
            }
        }

        public void LoadPreview(string url)
        {
            if (url.Contains("http"))
            {
                var op = DisplayOption.GetDefaultDisplayOption();
                op.isMemoryCache = false;
                ImageLoader.GetInstance().GetTexture(url, (tex) =>
                {
                    if (tex != null)
                    {
                        if (this != null)
                        {
                            if (isActiveAndEnabled)
                            {
                                values.Add(tex);
                            }
                            else
                            {
                                DestroyImmediate(tex);
                            }
                        }
                        else
                        {
                            DestroyImmediate(tex);
                        }
                    }
                }, op);
            }
            else
            {
                LoadFileImage(url);
            }
        }

        private void LoadFileImage(string path)
        {
            var fs = new FileStream(path, FileMode.Open);
            byte[] bytes = new byte[fs.Length];
            fs.Read(bytes, 0, (int)fs.Length);
            if (this != null && isActiveAndEnabled)
            {
                var texture = new Texture2D(1, 1);
                texture.LoadImage(bytes);
                values.Add(texture);
            }
            fs.Close();
            fs.Dispose();
        }

    }
}
