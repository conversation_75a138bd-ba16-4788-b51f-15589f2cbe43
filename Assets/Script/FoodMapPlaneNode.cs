
using System;
using UnityEngine;
using UnityEngine.UI;

namespace Com.SceneConsole.Arte
{
    public class FoodMapPlaneNode: MonoBehaviour
    {
        public static readonly string Tag = "FoodMapPlaneNode";
        [HideInInspector]
        public Transform canvas;
        [HideInInspector]
        public Transform image;

        
        [HideInInspector]
        public Transform canvas1;
        [HideInInspector]
        public Transform videoPlayer;
        
        public Transform target;
        public Action OnNavAction;
        public virtual void Awake()
        {
            canvas = transform.Find("Canvas");
            image = canvas.Find("Image");
            
            canvas1 = transform.Find("Canvas (1)");
            videoPlayer = canvas1.Find("Video Player");
        }
        // Start is called before the first frame update
        void Start()
        {
            canvas.gameObject.SetActive(true);
            canvas1.gameObject.SetActive(false);
            videoPlayer.gameObject.SetActive(false);
        }
        private void ShowVideo()
        {
            canvas.gameObject.SetActive(false);
            canvas1.gameObject.SetActive(true);
            videoPlayer.gameObject.SetActive(true);
        }
        private void ShowMain()
        {
            canvas.gameObject.SetActive(true);
            canvas1.gameObject.SetActive(false);
            videoPlayer.gameObject.SetActive(false);
        }

        private void StartNav()
        {
            OnNavAction?.Invoke();
        }
        private void Update()
        {
            
        }

        public void Tapped(Transform t)
        {
            if (t.name.Equals("Canvas"))
            {
                ShowVideo();
            } else if (t.name.Equals("Canvas (1)"))
            {
                OnNavAction?.Invoke();
            }
            else
            {
                ShowMain();
            }
        }
    }
}