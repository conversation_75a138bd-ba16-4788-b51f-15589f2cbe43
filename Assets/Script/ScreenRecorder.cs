using System;
using System.Collections;

using Unity.Collections.LowLevel.Unsafe;

using UnityEngine;
using UnityEngine.XR.ARFoundation;
using UnityEngine.XR.ARSubsystems;

namespace Com.SceneConsole.Arte
{
    /// <summary>
    /// 视频录制 屏幕截图
    /// </summary>
    public class ScreenRecorder : MonoBehaviour
    {
        /// <summary>
        /// 摄像机
        /// </summary>
        public ARCameraManager cameraManager;

        /// <summary>
        /// 只显示artefact layer的摄像机
        /// </summary>
        //public Camera arteCamera;

        #region Private Var

        private Texture2D renderTexture;
        private XRCpuImage.ConversionParams conversionParams;
        private IntPtr intPtr;

        private bool isRecording;
        private float captureTime;

        private bool isScreenShot;

        private int recordType;
        private bool rawImageData; //是否是摄像头原始数据

        #endregion

        //private unsafe void OnGUI()
        //{
        //    if (GUI.Button(new Rect(10, 400, 200, 100), "Record Video"))
        //    {
        //        if (isRecording)
        //        {
        //            FinishRecord();
        //        }
        //        else
        //        {
        //            StartRecord(true);
        //        }
        //    }
        //    if (GUI.Button(new Rect(10, 500, 200, 100), "Record Video - Arte"))
        //    {
        //        if (isRecording)
        //        {
        //            FinishRecord();
        //        }
        //        else
        //        {
        //            StartRecord(false);
        //        }
        //    }
        //}

        public void ShowOrHideArtefact(string value)
        {
            CameraManager.Instance.ShowOrHideArtefact(value);
        }

        public void SwitchFocusMode(string value)
        {
            var arg = bool.Parse(value);
            cameraManager.autoFocusRequested = arg;
        }

        public void StartRecord(string value)
        {
            if (!isRecording)
            {
                recordType = int.Parse(value);
                rawImageData = recordType == 0;
                isRecording = true;
            }
        }

        public void FinishRecord()
        {
            if (isRecording)
            {
                isRecording = false;
                if (renderTexture != null)
                {
                    Destroy(renderTexture);
                }
                NativeAPI.StopRecordVideo();
            }
        }

        public void TakeScreenShot(string value)
        {
            if (!isScreenShot)
            {
                recordType = int.Parse(value);
                rawImageData = recordType == 0;
                isScreenShot = true;
            }
        }

        private void Update()
        {
            if (isRecording)
            {
                float time = Time.time;
                if (time < captureTime)
                {
                    return;
                }
                else
                {
                    captureTime = time + 0.033f;
                }

                if (!rawImageData)
                {
                    StartCoroutine(ScreenRecordRenderImage());
                }
                else
                {
                    ScreenRecordRawImage();
                }
            }
            if (isScreenShot)
            {
                isScreenShot = false;
                if (!rawImageData)
                {
                    StartCoroutine(ScreenShotRenderImage());
                }
                else
                {
                    ScreenShotRawImage();
                }
            }
        }

        /// <summary>
        /// 获取摄像头原始数据
        /// </summary>
        private unsafe void ScreenShotRawImage()
        {

            if (!cameraManager.TryAcquireLatestCpuImage(out XRCpuImage image))
            {
                return;
            }

            if (renderTexture == null)
            {
                renderTexture = new Texture2D(image.width, image.height, TextureFormat.BGRA32, false);
                conversionParams = new XRCpuImage.ConversionParams(image, TextureFormat.BGRA32);
                intPtr = new IntPtr(renderTexture.GetRawTextureData<byte>().GetUnsafePtr());
            }

            var rawTextureData = renderTexture.GetRawTextureData<byte>();
            try
            {
                image.Convert(conversionParams, intPtr, rawTextureData.Length);
            }
            finally
            {
                image.Dispose();
            }

            renderTexture.Apply();

            byte[] rawData = renderTexture.EncodeToPNG();
            NativeAPI.ScreenDidShot(rawData, rawData.Length, recordType);

            if (renderTexture != null)
            {
                Destroy(renderTexture);
            }
        }

        /// <summary>
        /// 获取屏幕渲染结果
        /// </summary>
        /// <returns></returns>
        private IEnumerator ScreenShotRenderImage()
        {
            // Wait for screen rendering to complete
            yield return new WaitForEndOfFrame();

            if (renderTexture == null)
            {
                renderTexture = new Texture2D(Screen.width, Screen.height, TextureFormat.BGRA32, false);
            }

            renderTexture.ReadPixels(new Rect(0, 0, Screen.width, Screen.height), 0, 0);
            renderTexture.Apply();

            byte[] rawData = renderTexture.EncodeToJPG();
            NativeAPI.ScreenDidShot(rawData, rawData.Length, recordType);

            if (renderTexture != null)
            {
                Destroy(renderTexture);
            }
        }

        /// <summary>
        /// 获取摄像头原始数据
        /// </summary>
        private unsafe void ScreenRecordRawImage()
        {
            if (!cameraManager.TryAcquireLatestCpuImage(out XRCpuImage image))
            {
                return;
            }

            if (isRecording)
            {
                if (renderTexture == null)
                {
                    if (Application.platform == RuntimePlatform.Android)
                    {
                        renderTexture = new Texture2D(image.width, image.height, TextureFormat.ARGB32, false);
                        conversionParams = new XRCpuImage.ConversionParams(image, TextureFormat.ARGB32);
                    }
                    else
                    {
                        renderTexture = new Texture2D(image.width, image.height, TextureFormat.BGRA32, false);
                        conversionParams = new XRCpuImage.ConversionParams(image, TextureFormat.BGRA32);
                    }
                    intPtr = new IntPtr(renderTexture.GetRawTextureData<byte>().GetUnsafePtr());
                    NativeAPI.StartRecordVideo(image.width, image.height, recordType);
                }

                var rawTextureData = renderTexture.GetRawTextureData<byte>();
                try
                {
                    image.Convert(conversionParams, intPtr, rawTextureData.Length);
                }
                finally
                {
                    image.Dispose();
                }

                renderTexture.Apply();

                NativeAPI.SendVideoData(renderTexture.GetRawTextureData(), rawTextureData.Length);
            }
        }

        /// <summary>
        /// 获取屏幕渲染结果
        /// </summary>
        /// <returns></returns>
        private IEnumerator ScreenRecordRenderImage()
        {
            // Wait for screen rendering to complete
            yield return new WaitForEndOfFrame();

            if (isRecording)
            {
                if (renderTexture == null)
                {
                    renderTexture = new Texture2D(Screen.width, Screen.height, TextureFormat.BGRA32, false);
                    NativeAPI.StartRecordVideo(Screen.width, Screen.height, recordType);
                }

                renderTexture.ReadPixels(new Rect(0, 0, Screen.width, Screen.height), 0, 0);
                renderTexture.Apply();

                byte[] rawData = renderTexture.GetRawTextureData();
                NativeAPI.SendVideoData(rawData, rawData.Length);
            }
        }

    }
}

