using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Serialization;

namespace Com.SceneConsole.Arte
{
    public enum MegaMapType
    {
        h203,
        fanhaiB1110,
        xihu,
        street22
    }

    public class MegaBlockSceneRoot : MonoBehaviour
    {
        public MegaMapType megaMapType = MegaMapType.h203;
        public GameObject modelContainerGameObject;
        public float modelScaleToBigDistance = 100f;
        public float modelScaleToSmallDistance = 101f;
        public GameObject skyDoomGameObject;
        public GameObject arActivityContainerGameObject;

        private string currentMegaActivityId = "";

        // Start is called before the first frame update
        void Start()
        {
        }

        // Update is called once per frame
        void Update()
        {
        }

        public void SetCurrentMegaMapType(MegaMapType type)
        {
            modelContainerGameObject.SetActive(false);
            if (skyDoomGameObject)
            {
                skyDoomGameObject.transform.parent.gameObject.SetActive(false);
            }

            arActivityContainerGameObject.SetActive(false);
            currentMegaActivityId = "";
            gameObject.SetActive(megaMapType == type);
        }

        public Transform FindActivityDefaultNavTarget()
        {
            if (gameObject.activeInHierarchy)
            {
                foreach (Transform item in arActivityContainerGameObject.transform)
                {
                    if (item.name == "Activity" + currentMegaActivityId)
                    {
                        var target = item.transform.Find("DefaultNavTarget");
                        if (target)
                        {
                            Debug.Log("FindActivityDefaultNavTarget" + target.name);
                            return target;
                        }
                    }
                }
            }
            return null;
        }
        public void SetCurrentMegaActivity(string id)
        {
            currentMegaActivityId = id;
            if (id == "")
            {
                modelContainerGameObject.SetActive(true);
                arActivityContainerGameObject.SetActive(false);
                // ScanAndShow();
                return;
            }
            
            bool isFound = false;
            foreach (Transform item in arActivityContainerGameObject.transform)
            {
                item.gameObject.SetActive(item.name == "Activity" + id);
                if (item.name == "Activity" + id)
                {
                    isFound = true;
                }
            }

            if (!isFound && arActivityContainerGameObject.transform.childCount > 0)
            {
                arActivityContainerGameObject.transform.GetChild(0).gameObject.SetActive(true);
            }

            ScanAndShow();
        }

        public void ScanAndShow()
        {
            // Debug.Log("ScanAndShow");
            if (!isActiveAndEnabled)
            {
                return;
            }

            modelContainerGameObject.SetActive(false);
            if (skyDoomGameObject)
            {
                skyDoomGameObject.transform.parent.gameObject.SetActive(false);
            }

            arActivityContainerGameObject.SetActive(false);
            if (GetComponentInChildren<PosMeshScanEffect>(false))
            {
                GetComponentInChildren<PosMeshScanEffect>(false).resetScan();
            }

            Invoke(nameof(DelayToShowModels), 3f);
            Debug.Log("DelayToShowModels And Skydoom");
        }

        private void DelayToShowModels()
        {
            modelContainerGameObject.SetActive(currentMegaActivityId == "");
            arActivityContainerGameObject.SetActive(currentMegaActivityId.Length > 0);
            if (skyDoomGameObject)
            {
                skyDoomGameObject.transform.parent.gameObject.SetActive(true);
            }

            ShowMegaModelsAnimation();
        }

        public void SetMegaSceneActive(bool active)
        {
            if (modelContainerGameObject)
            {
                modelContainerGameObject.SetActive(active);
            }

            if (arActivityContainerGameObject)
            {
                arActivityContainerGameObject.SetActive(false);
            }

            if (skyDoomGameObject)
            {
                skyDoomGameObject.transform.parent.gameObject.SetActive(active);
            }
        }

        private void ShowMegaModelsAnimation()
        {
            if (gameObject.activeInHierarchy == false || isActiveAndEnabled == false)
            {
                return;
            }

            if (modelContainerGameObject)
            {
                foreach (Transform child in modelContainerGameObject.transform)
                {
                    AddScaleAnimationComponent(child.gameObject);
                }
            }

            if (arActivityContainerGameObject)
            {
                foreach (Transform child0 in arActivityContainerGameObject.transform)
                {
                    foreach (Transform child in child0.transform)
                    {
                        AddScaleAnimationComponent(child.gameObject);
                    }
                }
            }
        }

        private void AddScaleAnimationComponent(GameObject childGameObject)
        {
            var canAddScale = true;
            if (childGameObject.GetComponent<GameObjectModelTypeHolder>())
            {
                if (childGameObject.GetComponent<GameObjectModelTypeHolder>().gameObjectModelType ==
                    GameObjectModelTypeHolder.GameObjectModelType.Container)
                {
                    canAddScale = false;
                }
            }
            else
            {
                canAddScale = false;
            }
            if (!childGameObject.name.Contains("Test") && canAddScale)
            {
                if (!childGameObject.GetComponent<ModelScaleAnimation>())
                {
                    LeanTween.cancel(childGameObject, callOnComplete: true);
                    childGameObject.AddComponent<ModelScaleAnimation>();
                    childGameObject.GetComponent<ModelScaleAnimation>().originalScale =
                        childGameObject.transform.localScale;
                    childGameObject.GetComponent<ModelScaleAnimation>().scaleToBigDistance = modelScaleToBigDistance;
                    childGameObject.GetComponent<ModelScaleAnimation>().scaleToSmallDistance =
                        modelScaleToSmallDistance;
                }
                else
                {
                    LeanTween.cancel(childGameObject, callOnComplete: true);
                    childGameObject.GetComponent<ModelScaleAnimation>().ResetScaleState();
                }

                childGameObject.GetComponent<ModelScaleAnimation>().FirstScaleToBig();
            }
        }
    }
}