using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace Com.SceneConsole.Arte
{
    /// <summary>
    /// 聚焦扩散
    /// </summary>
    public class RaycastPulse : MonoBehaviour
    {
        private GameObject[] list;

        public GameObject mainObject;

        public Camera mainCamera;

        public Text text;

        public float radius = 1;

        private bool start;

        //private void OnGUI()
        //{
        //    if (G<PERSON>.<PERSON>(new Rect(10, 600, 200, 100), "Test"))
        //    {
        //        start = true;
        //    }
        //    if (GUI.Button(new Rect(210, 600, 200, 100), "Reset"))
        //    {
        //        start = false;
        //        ResetArte();
        //    }
        //}

        private void Start()
        {
            mainCamera = GetComponent<Camera>();
            list = GameObject.FindGameObjectsWithTag("Artefact");
        }

        private void Update()
        {
            if (start)
            {
                FocusArte();
            }
        }

        private void FocusArte()
        {
            float limit = Mathf.Asin(radius / Vector3.Distance(mainCamera.transform.position, mainObject.transform.position));
            Vector3 mainVector = mainObject.transform.position - mainCamera.transform.position;
            for (int i = 0; i < list.Length; i++)
            {
                Vector3 vector = list[i].transform.position - mainCamera.transform.position;
                float angle = Mathf.Acos(Vector3.Dot(vector.normalized, mainVector.normalized));
                if (angle < limit)
                {
                    list[i].GetComponent<Renderer>().material.color = Color.green;
                    float angleToRotate = limit - angle;
                    Vector3 cross = Vector3.Cross(vector.normalized, mainVector.normalized);
                    list[i].transform.RotateAround(mainCamera.transform.position, cross, -angleToRotate);
                }
                else
                {
                    list[i].GetComponent<Renderer>().material.color = Color.blue;
                }
            }
        }

        void ResetArte()
        {
            for (int i = 0; i < list.Length; i++)
            {
                list[i].GetComponent<Renderer>().material.color = Color.red;
            }
        }
    }
}
