using System.Collections;
using System.Collections.Generic;

using UnityEngine;

namespace Com.SceneConsole.Arte
{
    public class PoiPlaneNode : MonoBehaviour
    {
        public Sprite facility1;
        public Sprite facility2;
        public Sprite facility3;
        public Sprite facility4;
        public Sprite facility5;

        /// 漂浮动画初始位置
        [HideInInspector]
        public float periodFactor;
        private float deltaTime;

        [HideInInspector]
        public Transform canvas;
        [HideInInspector]
        public Transform imageBg;
        [HideInInspector]
        public Transform nameText;
        [HideInInspector]
        public Transform distanceText;
        [HideInInspector]
        public Transform typeImage;
        [HideInInspector]
        public Transform typeText;

        [HideInInspector]
        public PoiUnity Model
        {
            get => model;
            set
            {
                model = value;
                SetModel(value);
            }
        }

        /// <summary>
        /// 透明度 0 - 1
        /// </summary>
        public float Alpha
        {
            get => alpha;
            set
            {
                alpha = value;
                SetAlpha(value);
            }
        }

        /// <summary>
        /// 缩放 0 - 1
        /// </summary>
        public float Scale
        {
            get => scale;
            set
            {
                scale = value;
                SetScale(value);
            }
        }
        /// <summary>
        /// 边框颜色
        /// </summary>
        public Color Color
        {
            get => color;
            set
            {
                color = value;
                SetColor(value);
            }
        }

        private Color color = new Color32(123, 0, 159, 255);
        private float alpha = 1.0f;
        private float scale = 1.0f;
        private PoiUnity model;


        public virtual void Awake()
        {
            canvas = transform.Find("Canvas");
            imageBg = canvas.Find("ImageBg");
            nameText = canvas.Find("NameText");
            distanceText = canvas.Find("DistanceText");
            typeImage = canvas.Find("TypeImage");
            typeText = typeImage.Find("TypeText");
        }
        // Start is called before the first frame update
        void Start()
        {
            periodFactor = Random.Range(0.2f, 0.5f);
            deltaTime = Random.Range(0f, 10f);
        }

        private void Update()
        {
            deltaTime += Time.deltaTime;
            var offset = new Vector3(0, Mathf.Sin(deltaTime * periodFactor) * 0.5f * scale, 0);
            transform.localPosition += offset;

        }
        void LateUpdate()
        {
            var cameraP = Camera.main.transform.position;
            var cameraAdjustP = new Vector3(cameraP.x, transform.position.y, cameraP.z);
            var targetP = transform.position * 2 - cameraAdjustP;
            transform.LookAt(targetP, Vector3.up);
        }
        private void SetAlpha(float a)
        {

            GetComponent<Renderer>().material.SetFloat("_Alpha", a);

        }

        private void SetScale(float s)
        {
            var vector = new Vector3(x: s * 1.5f, y: s * 1.5f, z: s * 1.5f);
            transform.localScale = vector;
        }

        private void SetColor(Color c)
        {
            imageBg.GetComponent<UnityEngine.UI.Image>().color = c;
            if (model.facility == 0)
            {
                typeImage.GetComponent<UnityEngine.UI.Image>().color = c;
            }

        }
        public void SetDisplayDistance(int d)
        {
            distanceText.GetComponent<UnityEngine.UI.Text>().text = FormatDistance(d);
        }

        public void SetModel(PoiUnity i)
        {
            //Debug.Log(i.name);
            if (i.facility != 0)
            {
                if (i.facility == 1)//1 医院；2 派出所；3 加油站；4 卫生间；5 便利店
                {
                    nameText.GetComponent<TMPro.TextMeshProUGUI>().text = "医院";
                    typeImage.GetComponent<UnityEngine.UI.Image>().sprite = facility1;
                }
                else if (i.facility == 2)
                {
                    nameText.GetComponent<TMPro.TextMeshProUGUI>().text = "派出所";
                    typeImage.GetComponent<UnityEngine.UI.Image>().sprite = facility2;
                }
                else if (i.facility == 3)
                {
                    nameText.GetComponent<TMPro.TextMeshProUGUI>().text = "加油站";
                    typeImage.GetComponent<UnityEngine.UI.Image>().sprite = facility3;
                }
                else if (i.facility == 4)
                {
                    nameText.GetComponent<TMPro.TextMeshProUGUI>().text = "卫生间";
                    typeImage.GetComponent<UnityEngine.UI.Image>().sprite = facility4;
                }
                else if (i.facility == 5)
                {
                    nameText.GetComponent<TMPro.TextMeshProUGUI>().text = "便利店";
                    typeImage.GetComponent<UnityEngine.UI.Image>().sprite = facility5;
                }
            }
            else
            {

                nameText.GetComponent<TMPro.TextMeshProUGUI>().text = i.name;
                var types = i.type.Split('|');
                if (types.Length > 0)
                {
                    var subTypes = types[0].Split('；', ';');
                    typeText.GetComponent<UnityEngine.UI.Text>().text = subTypes[0];
                }
            }

            distanceText.GetComponent<UnityEngine.UI.Text>().text = FormatDistance(i.distance);
            ColorUtility.TryParseHtmlString(i.hexColor, out Color color);
            SetColor(color);
        }
        private string FormatDistance(int dis)
        {
            //if (dis == null)
            //{
            //    return "";
            //}
            if (dis >= 100000)
            {
                var num = dis / 1000;
                return num.ToString() + "km";
            }
            else if (dis >= 1000)
            {
                var num = (float)dis / 1000.0f;
                return num.ToString("#.#") + "km";
            }
            else if (dis < 20)
            {
                return "已在附近";
            }
            else
            {
                return dis.ToString() + "m";
            }

        }

        public override bool Equals(object obj)
        {
            PoiPlaneNode e = obj as PoiPlaneNode;
            return model.id == e.model.id;
        }

        public override int GetHashCode()
        {
            return model.id.GetHashCode();
        }
    }
}
