using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.AI;

namespace Com.SceneConsole.Arte
{
    public class ShowGoldenPath : MonoBehaviour
    {
        public Transform target;
        public LineRenderer line;
        private float elapsed;
        private float cursorFrequency;
        private List<Vector3> _pathPoints = new List<Vector3>(); //保存计算完成的点

        void Start()
        {
            elapsed = 0.0f;
        }
        
        void Update()
        {
            if (target == null)
            {
                line.positionCount = 0;
                _pathPoints.Clear();
                DestroyImmediate(gameObject.GetComponent<ScreenCursor>());
                return;
            }
            if (line.gameObject.activeInHierarchy && target.gameObject.activeInHierarchy)
            {
                // Update the way to the goal every second.
                elapsed += Time.deltaTime;
                if (elapsed > 1.0f)
                {
                    elapsed = 0.0f;
                    CalculatePath(transform.position, target.position);
                }
            }
            else
            {
                DestroyImmediate(gameObject.GetComponent<ScreenCursor>());
            }
           

            
            if (cursorFrequency > 0.2f)
            {
                ShowOrHideCursor();
                cursorFrequency = 0;
            }
            else
            {
                cursorFrequency += Time.deltaTime;
            }
        }

        private void DrawLine(Vector3 start, Vector3 end)
        {
            line.startColor = Color.white;
            line.positionCount = 2;
            line.SetPosition(0, start + Vector3.up * 0.1f);
            line.SetPosition(1, end + Vector3.up * 0.1f);
        }

        private void DrawLineFromPoints()
        {
            if (line == null)
                return;

            if (_pathPoints.Count > 0)
            {
                line.positionCount = _pathPoints.Count;
                for (int i = 0; i < _pathPoints.Count; i++)
                {
                    // 添加一个小的高度偏移，使路径在地面上方显示
                    Vector3 position = _pathPoints[i] + Vector3.up * 0.1f;
                    line.SetPosition(i, position);
                }
            }
        }



//计算路径
        public void CalculatePath(Vector3 start, Vector3 end)
        {
            _pathPoints.Clear();
            line.positionCount = 0;
            start = GetRightPoint(start);
            end = GetRightPoint(end);
            float sqrDistance = (start - end).sqrMagnitude;
            if (sqrDistance < 64)
            {
                DrawLine(start, end);
                return;
            }

            StartCoroutine(GetPath(start, end));
        }

// 获取路径
        private IEnumerator GetPath(Vector3 start, Vector3 end)
        {
            //计算路径
            NavMeshPath path = new NavMeshPath();
            NavMesh.CalculatePath(start, end, NavMesh.AllAreas, path);
            // Debug.Log(path.status.ToString() + ";" + path.corners.Length.ToString());
            switch (path.status)
            {
                case NavMeshPathStatus.PathComplete: //计算完成
                    SavePoints(path.corners); //保存点
                    DrawLineFromPoints();
                    line.startColor = Color.green;
                    yield break;
                case NavMeshPathStatus.PathInvalid: //计算失败
                    Debug.Log("路径计算失败...");
                    line.startColor = Color.red;
                    yield break;
                case NavMeshPathStatus.PathPartial: //计算未完成，继续计算
                    if (path.corners.Length <= 1)
                        yield break;
                    line.startColor = Color.yellow;
                    SavePoints(path.corners); //保存点
                    DrawLineFromPoints();
                    yield return GetPath(path.corners.Last(), end); //继续计算
                    yield break;
                default:
                    throw new ArgumentOutOfRangeException();
            }
        }

//获得一个在NavMesh上的采样点
        private Vector3 GetRightPoint(Vector3 point)
        {
            if (!NavMesh.SamplePosition(point, out var navHit, 100, NavMesh.AllAreas))
                return point;
            return navHit.position;
        }

//保存点
        private void SavePoints(params Vector3[] points)
        {
            //去重
            if (_pathPoints.Count != 0)
                _pathPoints.RemoveAt(_pathPoints.Count - 1);

            _pathPoints.AddRange(points);
        }


        private void ShowOrHideCursor()
        {
            // for (int i = 0; i < _pathPoints.Count; i++)
            // {
            //     Vector3 point = _pathPoints[i];
            //     Vector3 worldPos = transform.TransformPoint(point);
            //     if (IsInView(worldPos))
            //     {
            //         DestroyImmediate(gameObject.GetComponent<ScreenCursor>());
            //         //Debug.Log("InView");
            //         return;
            //     }
            // }
            if (target != null)
            {
                Vector3 worldPos = target.position;
                if (IsInView(worldPos))
                {
                    DestroyImmediate(gameObject.GetComponent<ScreenCursor>());
                    //Debug.Log("InView");
                    return;
                }
                
                if (transform.GetComponent<ScreenCursor>() == null)
                {
                    gameObject.AddComponent<ScreenCursor>();
                }
                gameObject.GetComponent<ScreenCursor>().target = target;
            }
            else
            {
                DestroyImmediate(gameObject.GetComponent<ScreenCursor>());
            }

            //Debug.Log("NoNodeInView");
        }

        public bool IsInView(Vector3 worldPos)
        {
            if (Camera.main != null)
            {
                Transform camTransform = Camera.main.transform;
                Vector2 viewPos = Camera.main.WorldToViewportPoint(worldPos);
                Vector3 dir = (worldPos - camTransform.position).normalized;
                float dot = Vector3.Dot(camTransform.forward, dir); //判断物体是否在相机前面

                if (dot > 0 && viewPos.x >= 0 && viewPos.x <= 1 && viewPos.y >= 0 && viewPos.y <= 1)
                    return true;
            }

            return false;
        }
    }
}