using System.Collections;
using System.Collections.Generic;

using UnityEngine;
using UnityEngine.Networking;

namespace Com.SceneConsole.Arte
{
    [DisallowMultipleComponent]
    public class AudioManager : Singleton<AudioManager>
    {
        /// <summary>
        /// 背景音效 循环播放
        /// </summary>
        public AudioSource bgAudioSource;
        public AudioSource bgAudioSource2;

        /// <summary>
        /// 普通音效 播放一次
        /// </summary>
        public List<AudioSource> audioSources = new List<AudioSource>();

        /// <summary>
        /// 循环音效 循环播放
        /// </summary>
        public AudioSource loopSource;

        public float speed = 0.5f;

        private AssetBundle asset;

        private bool bgIsMute;
        private bool effectIsMute;

        private void Awake()
        {
            AudioSettings.outputSampleRate = 44100;
        }

        /// <summary>
        /// 加载网络音频包
        /// </summary>
        /// <param name="url"></param>
        /// <param name="version"></param>
        public void LoadSoundAssertBundle(string json)
        {
            var args = json.Split(',');
            StartCoroutine(LoadAssertBundle(args[0], uint.Parse(args[1])));
        }

        private IEnumerator LoadAssertBundle(string url, uint version)
        {
#if DEBUG
            Debug.Log("加载音频包");
#endif
            UnityWebRequest www = UnityWebRequestAssetBundle.GetAssetBundle(url, version, 0);
            yield return www.SendWebRequest();

            if (www.isDone && !www.isNetworkError)
            {
#if DEBUG
                Debug.Log("加载音频完成");
#endif
                asset = DownloadHandlerAssetBundle.GetContent(www);
            }
        }

        //静音
        public void Mute()
        {
            bgAudioSource.volume = 0;
            bgAudioSource2.volume = 0;
            loopSource.volume = 0;
            foreach (var item in audioSources)
            {
                item.volume = 0;
            }
        }

        //取消静音
        public void UnMute()
        {
            bgAudioSource.volume = 1;
            bgAudioSource2.volume = 1;
            loopSource.volume = 1;
            foreach (var item in audioSources)
            {
                item.volume = 1;
            }
        }

        public void SwitchBgMute(string mute)
        {
            bgIsMute = bool.Parse(mute);
            transform.GetComponent<AudioListener>();

        }

        public void SwitchEffectMute(string mute)
        {
            effectIsMute = bool.Parse(mute);
        }

        public void PlayBgAudioClip(string url)
        {
            if (bgIsMute)
            {
                return;
            }
            if (asset != null)
            {
                AudioClip clip = asset.LoadAsset<AudioClip>(url);
                bgAudioSource.clip = clip;
                bgAudioSource2.clip = clip;
            }
            else
            {
                AudioClip clip = Resources.Load<AudioClip>("Sound/" + url);
                bgAudioSource.clip = clip;
                bgAudioSource2.clip = clip;
            }
            bgAudioSource.Play();
            bgAudioSource2.PlayDelayed(189.008f);
        }

        public void StopBgAudioClip()
        {
            bgAudioSource.Stop();
            bgAudioSource2.Stop();
        }

        /// <summary>
        /// 播放循环音效
        /// </summary>
        /// <param name="url"></param>
        public void PlayLoopAudioClip(string url)
        {
            if (effectIsMute)
            {
                return;
            }
            if (asset != null)
            {
                AudioClip clip = asset.LoadAsset<AudioClip>(url);
                loopSource.clip = clip;
            }
            else
            {
                AudioClip clip = Resources.Load<AudioClip>("Sound/" + url);
                loopSource.clip = clip;
            }
            audioChangeStatus = 1;
            loopSource.volume = 0;
            loopSource.Play();
        }

        public void UpdateLoopAudioVolume(string volume)
        {
            if (audioChangeStatus == 2)
            {
                loopSource.volume = float.Parse(volume);
            }
        }

        public void StopLoopAudioClip()
        {
            audioChangeStatus = 3;
        }

        private int audioChangeStatus = 0; //0 停止 1 淡入 2 音量改变 3 淡出

        private void Update()
        {
            if (audioChangeStatus == 1)
            {
                loopSource.volume = Mathf.Lerp(loopSource.volume, 0.5f, Time.deltaTime * speed);
                if (loopSource.volume >= 0.49f)
                {
                    audioChangeStatus = 2;
                }
            }
            if (audioChangeStatus == 3)
            {
                loopSource.volume = Mathf.Lerp(loopSource.volume, 0.0f, Time.deltaTime * speed);
                if (loopSource.volume <= 0.01f)
                {
                    audioChangeStatus = 0;
                    loopSource.clip = null;
                }
            }
        }

        public void PlayAudioClip(string url)
        {
            if (effectIsMute)
            {
                return;
            }
            AudioSource source = null;
            foreach (var item in audioSources)
            {
                if (!item.isPlaying)
                {
                    source = item;
                    break;
                }
            }
            if (source == null)
            {
                source = transform.gameObject.AddComponent<AudioSource>();
                audioSources.Add(source);
            }
            if (asset != null)
            {
                AudioClip clip = asset.LoadAsset<AudioClip>(url);
                source.clip = clip;
                source.Play();
            }
            else
            {
                AudioClip clip = Resources.Load<AudioClip>("Sound/" + url);
                source.clip = clip;
                source.Play();
            }
        }

        private IEnumerator LoadWebAudio(string url, AudioSource source, bool bg)
        {
            UnityWebRequest www = new UnityWebRequest(url);
            DownloadHandlerAudioClip handler = new DownloadHandlerAudioClip(url, AudioType.WAV);
            www.downloadHandler = handler;
            yield return www.SendWebRequest();

            if (www.isDone && !www.isNetworkError)
            {
                if (bg)
                {
                    source.Stop();
                    source.clip = handler.audioClip;
                    source.Play();
                }
                else
                {
                    source.PlayOneShot(handler.audioClip);
                }
            }

            handler.Dispose();
            www.Dispose();
        }

        //private void OnGUI()
        //{
        //    if (GUI.Button(new Rect(10, 100, 200, 40), "Play"))
        //    {
        //        //PlayLoopAudioClip(@"https://ar-reader.oss-cn-beijing.aliyuncs.com/audio/arte/audio_1592201686522537500.wav");
        //        //UpdateLoopAudioVolume("0.8");
        //        LoadSoundAssertBundle(@"https://arte.oss-cn-beijing.aliyuncs.com/sound.unity3d,1");
        //        //PlayAudioClip("createArteSuccess");
        //    }
        //    if (GUI.Button(new Rect(10, 200, 200, 40), "PlayLoop"))
        //    {
        //        //PlayAudioClip(@"https://ar-reader.oss-cn-beijing.aliyuncs.com/audio/arte/audio_1592188771272873000.wav");
        //        //PlayLoopAudioClip("gestureChange");
        //        PlayAudioClip("msgNotify");
        //    }
        //}

    }
}
