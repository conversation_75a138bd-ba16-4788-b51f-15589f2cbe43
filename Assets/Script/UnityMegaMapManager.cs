using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using EasyAR.Mega.Scene;

namespace Com.SceneConsole.Arte
{
    public class UnityMegaMapManager : MonoBehaviour
    {
        public GameObject easyARSession;
        
        public MegaBlockSceneRoot xihuPrefab;
        public MegaBlockSceneRoot h203Prefab;
        public MegaBlockSceneRoot fanhaiB1110Prefab;
        public MegaBlockSceneRoot street22Prefab;
        
        
        private GameObject easyARMegaBlocks;
        private List<MegaBlockSceneRoot> megaModelManagers;
        private easyar.MegaBlockTrackerFrameFilter megaTracker;
        private easyar.MegaTrackerLocalizationStatus megaBlockLocalizationStatus;
        private int wakingUpCount;
        private bool firstFound;
        // Start is called before the first frame update
        private void Awake()
        {
            Debug.Log("Unity Awake UnityMegaMapManager ");
            easyARMegaBlocks = easyARSession.GetComponentInChildren<BlockHolder>(true).BlockRoot.gameObject;
            easyARSession.GetComponent<easyar.ARSession>().StateChanged += EasyARSessionOnStateChanged; 
            megaTracker = easyARSession.GetComponentInChildren<easyar.MegaBlockTrackerFrameFilter>(true);
            if (megaTracker)
            {
                megaTracker.LocalizationRespond += HandleLocalizationStatusChange;
            }
            megaModelManagers = easyARMegaBlocks.GetComponentsInChildren<MegaBlockSceneRoot>(true);
            InstantiateMegaBlockSceneRoot(MegaMapType.h203, h203Prefab);
        }
        void Start()
        {

        }

       
         public void SwitchEasyARMegaLocationInternal(string n)
        {
            if (n == "h203")
            {
                Debug.Log("SwitchEasyARMegaLocationInternal " + h203Prefab);
                megaTracker.ServiceConfig.ServerAddress = "https://clsv3-api.easyar.com";
                megaTracker.ServiceConfig.AppID = "21a4370a4d3d4f4cadb340760ac41b0c";
                InstantiateMegaBlockSceneRoot(MegaMapType.h203, h203Prefab);
            } else if (n == "fanhaiB1110")
            {
                megaTracker.ServiceConfig.ServerAddress = "https://clsv3-api.easyar.com";
                megaTracker.ServiceConfig.AppID = "f8bad7281bf5457fa901d23c0b0ccaa0";
                InstantiateMegaBlockSceneRoot(MegaMapType.fanhaiB1110, fanhaiB1110Prefab);
            } else if (n == "xihu")
            {
                megaTracker.ServiceConfig.ServerAddress = "https://clsv3-api.easyar.com";
                megaTracker.ServiceConfig.AppID = "5a9a43baa7d840428b6efc37b97f5cc8";
                InstantiateMegaBlockSceneRoot(MegaMapType.xihu, xihuPrefab);
            } else if (n == "street22")
            {
                megaTracker.ServiceConfig.ServerAddress = "https://clsv3-api.easyar.com";
                megaTracker.ServiceConfig.AppID = "9796d8aec5b849d19953e0f526819eae";//美术馆all
                InstantiateMegaBlockSceneRoot(MegaMapType.street22, street22Prefab);
            }
            megaTracker.ResetTracker();
            
        }

        private void InstantiateMegaBlockSceneRoot(MegaMapType type, MegaBlockSceneRoot prefab)
        {
            var foundMap = false;
            Debug.Log("InstantiateMegaBlockSceneRoot 0");
            foreach (var item in megaModelManagers)
            {
                if (item.megaMapType == type)
                {
                    foundMap = true;
                    item.SetCurrentMegaMapType(type);
                    Debug.Log("InstantiateMegaBlockSceneRoot " + foundMap);
                }
                else
                {
                    Debug.Log("InstantiateMegaBlockSceneRoot Destroy0 ");
                    
                    if (item && item.transform)
                    {
                        item.transform.parent = null;
                    }
                    megaModelManagers.Remove(item);
                    Debug.Log("InstantiateMegaBlockSceneRoot Destroy1");
                    Destroy(item.gameObject);
                    Debug.Log("InstantiateMegaBlockSceneRoot Destroy2");
                }
            }
            Debug.Log("InstantiateMegaBlockSceneRoot 1");
            if (!foundMap)
            {
                Debug.Log("InstantiateMegaBlockSceneRoot Instantiate0");
                var item = Instantiate(prefab, easyARMegaBlocks.transform);
                megaModelManagers.Append(item);
                Debug.Log("InstantiateMegaBlockSceneRoot Instantiate1");
                item.SetCurrentMegaMapType(type);
                item.transform.SetParent(easyARMegaBlocks.transform);
                Debug.Log("InstantiateMegaBlockSceneRoot Instantiate2");
            }
            Debug.Log("InstantiateMegaBlockSceneRoot 3");
        }

        public void SetCurrentMegaActivity(string id)
        {
            Transform target = null;
            foreach (var item in megaModelManagers)
            {
                item.SetCurrentMegaActivity(id);
                var target0 = item.FindActivityDefaultNavTarget();
                if (target0)
                {
                    target = target0;
                }
            }

            if (target != null && id != "")
            {
                SwitchEasyARNavTarget(target);
            }
            else
            {
                SwitchEasyARNavTarget(null);
            }
        }
        public void ClearEasyARNavTarget()
        { 
            GetComponentInChildren<ShowGoldenPath>().target = null;
            NativeAPI.NaviTargetChangeWithName("");
        }
        public void SwitchEasyARNavTarget(Transform target)
        { 
            // Debug.Log("SwitchEasyARNavTarget " + target.name);
            GetComponentInChildren<ShowGoldenPath>().target = target;
            if (target == null)
            {
                NativeAPI.NaviTargetChangeWithName("");
            }
            else
            {
                NativeAPI.NaviTargetChangeWithName(target.name+target.position.ToString());
            }
            
        }
        public void SetEasyARMegaSkyDoomStatus(string setShow = "false")
        {
            var arg = bool.Parse(setShow);
            foreach (var item in megaModelManagers)
            {
                if (item.skyDoomGameObject)
                {
                    item.skyDoomGameObject.SetActive(arg);
                }
            }
            
        }

        public void StartEasyARSessionInternal()
        {
            Debug.Log("Unity StartEasyARSessionInternal ");
            
            easyARMegaBlocks.SetActive(true);
            easyARMegaBlocks.GetComponentInChildren<LineRenderer>(true).positionCount = 0;
            SetMegaSceneActive(false);
            easyARSession.SetActive(true);
            easyARSession.GetComponent<easyar.ARSession>().enabled = true;
            easyARSession.GetComponent<easyar.ARSession>().StartSession();
        }

        public void StopEasyARSessionInternal()
        {
            Debug.Log("Unity StopEasyARSessionInternal ");
            easyARSession.GetComponent<easyar.ARSession>().StopSession(false);
            easyARSession.GetComponent<easyar.ARSession>().enabled = false;
            easyARSession.SetActive(false);
            easyARMegaBlocks.GetComponentInChildren<LineRenderer>(true).positionCount = 0;
            easyARMegaBlocks.SetActive(false);
            firstFound = false;
            
        }
        private void EasyARSessionOnStateChanged(easyar.ARSession.SessionState args)
        {
#if DEBUG
            Debug.Log(args.ToString());
#endif
            if (args == easyar.ARSession.SessionState.Broken || args == easyar.ARSession.SessionState.None || args == easyar.ARSession.SessionState.Paused)
            {

            } else if (args == easyar.ARSession.SessionState.Assembling || args == easyar.ARSession.SessionState.Assembled || args == easyar.ARSession.SessionState.Ready)
            {

            } else if (args == easyar.ARSession.SessionState.Running)
            {
                NativeAPI.EasyARSessionDidStarted();
            }
            
        }

        private void HandleLocalizationStatusChange(easyar.MegaBlockLocalizationResponse response)
        {
            NativeAPI.EasyARMegaBlockLocalizationStatusChanged((int)response.Status);
            megaBlockLocalizationStatus = response.Status;
            var status = response.Status;
            wakingUpCount = status == easyar.MegaTrackerLocalizationStatus.WakingUp ? wakingUpCount + 1 : 0;
            if (wakingUpCount >= 5)
            {
                Debug.Log(
                    "Service is waking up, please wait patiently." + System.Environment.NewLine +
                    "服务正在唤醒中，请耐心等待。"
                );
            }

            if (status == easyar.MegaTrackerLocalizationStatus.QpsLimitExceeded)
            {
                Debug.Log("Too many users, please wait patiently." + System.Environment.NewLine +
                    "用户过多，请耐心等待。"
                );
            }

            if (status == easyar.MegaTrackerLocalizationStatus.Found)
            {
                // print("easyar.MegaTrackerLocalizationStatus.Found");
                if (firstFound == false)
                {
                    firstFound = true;
                    MegaBlockScanAndShow();
                }
            } else if (status == easyar.MegaTrackerLocalizationStatus.NotFound)
            {
                // // For Test;测试用；
                // firstFound = false;
                // SetMegaSceneActive(false);
            }
        }
        
        public void ShowMegaModelsScaleToBigAnimation()
        {
            MegaBlockScanAndShow();
        }
        // private void ShowMegaModelsWhenFound()
        // {
        //     // Debug.Log("延迟执行的函数");
        //     // Debug.Log(Camera.main.transform.position);
        //     // if (megaBlockLocalizationStatus == easyar.MegaTrackerLocalizationStatus.Found)
        //     // {
        //         foreach (var item in megaModelManagers)
        //         {
        //             item.ShowMegaModelsAnimation();
        //         }
        //     // }
        //     
        // }
        private void MegaBlockScanAndShow()
        {
            Debug.Log(megaModelManagers);
            foreach (var item in megaModelManagers)
            {
                item.ScanAndShow();
            }
            // Invoke(nameof(ShowMegaModelsWhenFound), 1.5f);
        }
        private void SetMegaSceneActive(bool active)
        {
            foreach (var item in megaModelManagers)
            {
                item.SetMegaSceneActive(active);
            }
        }
    }
}