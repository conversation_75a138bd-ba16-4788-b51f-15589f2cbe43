using System;
using System.Collections;
using System.Collections.Generic;

using Com.SceneConsole.Arte;

using UnityEngine;

using static UniGif;

public class GifDecoderManager : Singleton<GifDecoderManager>
{
    public class GifUrlData
    {
        public string uri;
        public byte[] data;

        public GifUrlData(string uri, byte[] data)
        {
            this.uri = uri;
            this.data = data;
        }
    }

    Dictionary<string, Action<List<GifTexture>, int, int, int>> myActionDict = new Dictionary<string, Action<List<GifTexture>, int, int, int>>();
    List<GifUrlData> dataList = new List<GifUrlData>();
    bool isDecoding = false;
    // Start is called before the first frame update
    void Start()
    {

    }

    // Update is called once per frame
    void Update()
    {

    }
    public void AddGif(string uri, byte[] data, Action<List<GifTexture>, int, int, int> callback)
    {
        myActionDict[uri] = callback;
        dataList.Add(item: new GifUrlData(uri, data));
        if (dataList.Count == 1 && isDecoding == false)
        {
            StartDecodeGifDataList();
        }
    }

    private void StartDecodeGifDataList()
    {
        if (dataList.Count == 0)
        {
            isDecoding = false;
            //Debug.Log("All Decode Finish");
            myActionDict.Clear();
            return;
        }
        isDecoding = true;
        var data = dataList[0];
        dataList.RemoveAt(0);
        var callback = myActionDict[data.uri];
        StartCoroutine(UniGif.GetTextureListCoroutine(data.data, (gifTexList, loopCount, width, height) =>
        {
            //Debug.Log("Decode Finish" + data.uri);
            callback?.Invoke(gifTexList, loopCount, width, height);
            this.myActionDict[data.uri] = null;
            StartDecodeGifDataList();
        },
    FilterMode.Point, TextureWrapMode.Clamp, false));
    }
}
