using System.Collections;
using System.Collections.Generic;
using System.IO;

using UnityEngine;
using UnityEngine.UI;

public class GifImage : MonoBehaviour
{
    public string gifName;
    public Sprite defaultSp;

    private SpriteRenderer m_rawImage;

    private List<UniGif.GifTexture> m_gifTextureList;
    // Delay time
    private float m_delayTime;
    // Texture index
    private int m_gifTextureIndex;

    void Start()
    {
        if (m_rawImage == null)
        {
            m_rawImage = GetComponent<SpriteRenderer>();
        }
    }

    private void OnEnable()
    {
        if (gifName != null)
        {
#if UNITY_ANDROID
            StartCoroutine(AsyncLoadGif(gifName));
#else
            LoadGif(gifName);
#endif
        }
    }

    private void OnDisable()
    {
        StopAllCoroutines();
        if (m_gifTextureList != null)
        {
            foreach (var item in m_gifTextureList)
            {
                DestroyImmediate(item.m_texture2d);
            }
        }
        m_gifTextureList = null;
        m_rawImage.sprite = defaultSp;
        Resources.UnloadUnusedAssets();
    }

    private void OnDestroy()
    {
        StopAllCoroutines();
        if (m_gifTextureList != null)
        {
            foreach (var item in m_gifTextureList)
            {
                DestroyImmediate(item.m_texture2d);
            }
        }
        Resources.UnloadUnusedAssets();
    }

    void Update()
    {
        if (m_rawImage == null || m_gifTextureList == null || m_gifTextureList.Count <= 0)
        {
            return;
        }
        if (m_delayTime > Time.time)
        {
            return;
        }
        // Change texture
        m_gifTextureIndex++;
        if (m_gifTextureIndex >= m_gifTextureList.Count)
        {
            m_gifTextureIndex = 0;
        }
        Texture2D tex = m_gifTextureList[m_gifTextureIndex].m_texture2d;
        var sp = Sprite.Create(tex, new Rect(0, 0, tex.width, tex.height), new Vector2(0.5f, 0.5f), Mathf.Max(tex.width, tex.height));
        m_rawImage.sprite = sp;
        m_delayTime = Time.time + m_gifTextureList[m_gifTextureIndex].m_delaySec;
    }

    public void LoadGif(string name)
    {
        byte[] data = File.ReadAllBytes(Application.streamingAssetsPath + "/" + name + ".gif");
        StartCoroutine(UniGif.GetTextureListCoroutine(data, (gifTexList, loopCount, width, height) =>
    {
        if (gifTexList != null)
        {
            if (this != null && isActiveAndEnabled)
            {
                m_gifTextureList = gifTexList;
            }
            else
            {
                foreach (var item in gifTexList)
                {
                    DestroyImmediate(item.m_texture2d);
                }
            }
        }
        else
        {
            Debug.LogError("Gif texture get error.");
        }
    }));
    }

    public IEnumerator AsyncLoadGif(string name)
    {
        using (WWW www = new WWW(Application.streamingAssetsPath + "/" + name + ".gif"))
        {
            yield return www;
            byte[] data = www.bytes;
            StartCoroutine(UniGif.GetTextureListCoroutine(data, (gifTexList, loopCount, width, height) =>
            {
                if (gifTexList != null)
                {
                    if (this != null && isActiveAndEnabled)
                    {
                        m_gifTextureList = gifTexList;
                    }
                    else
                    {
                        foreach (var item in gifTexList)
                        {
                            DestroyImmediate(item.m_texture2d);
                        }
                    }
                }
                else
                {
                    Debug.LogError("Gif texture get error.");
                }
            }));
        }
    }
}
