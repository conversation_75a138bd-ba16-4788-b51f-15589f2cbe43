using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;


namespace Com.SceneConsole.Arte
{
    public class ModelScaleAnimation : MonoBehaviour
    {
        public enum ScaleAnimationState
        {
            ScaleToBigAnimating,
            HasInBig,
            ScaleToSmallAnimating,
            HasInSmall,
        }
        public ScaleAnimationState scaleAnimationState = ScaleAnimationState.HasInBig;

        public bool isAnimating
        {
            get
            {
                return scaleAnimationState == ScaleAnimationState.ScaleToSmallAnimating || scaleAnimationState == ScaleAnimationState.ScaleToBigAnimating;
            }
        }
        
        public Vector3 originalScale = Vector3.one;
        public bool isScaleAnimationCheckBegin = false;
        public float scaleToBigDistance = 100f;
        public float scaleToSmallDistance = 101f;
        
        private float lastCameraCheckTime = 0f;
        private Vector3 lastCameraPosition;
        private const float CameraCheckInterval = 0.5f; // 相机位置检查间隔
        private const float DistanceCheckInterval = 0.3f; // 位置检查距离差值
        
        private void OnDisable()
        {
            ResetScaleState();
        }
        public void ResetScaleState()
        {
            isScaleAnimationCheckBegin = false;
            gameObject.transform.localScale = originalScale;
            scaleAnimationState = ScaleAnimationState.HasInBig;
            // Debug.Log("ResetScaleState + ScaleAnimationState.HasInBig");
        }

        // Update is called once per frame
        void Update()
        {
            if (!isScaleAnimationCheckBegin || !isActiveAndEnabled || !gameObject.activeInHierarchy)
            {
                return;
            }
            
            if (Camera.main != null && !isAnimating)
            {
                // 性能优化：只在相机位置发生显著变化或达到检查间隔时才重新计算
                var currentTime = Time.time;
                var cameraP = Camera.main.transform.position;

                bool shouldRecalculate = false;

                if (currentTime - lastCameraCheckTime >= CameraCheckInterval)
                {
                    if (Vector3.Distance(cameraP, lastCameraPosition) > DistanceCheckInterval)
                    {
                        shouldRecalculate = true;
                        lastCameraPosition = cameraP;
                    }
                    lastCameraCheckTime = currentTime;
                }

                if (shouldRecalculate)
                {
                    var distance = Vector3.Distance(cameraP, transform.position);
                    if (distance < scaleToBigDistance)
                    {
                        ScaleToBig();
                    }
                    else if (distance > scaleToSmallDistance)
                    {
                        ScaleToSmall();
                    }
                }
                
            }
        }
        
        public void ScaleToBig()
        {
            if (scaleAnimationState != ScaleAnimationState.HasInSmall)
            {
                return;
            }
            // gameObject.transform.localScale = originalScale * 0.01f;
            scaleAnimationState = ScaleAnimationState.ScaleToBigAnimating;
            LeanTween.scale(gameObject, originalScale * 2, 1.5f).setEase(LeanTweenType.easeInOutQuad).setOnComplete(() =>
            {
                LeanTween.scale(gameObject, originalScale, 1f).setEase(LeanTweenType.easeInOutQuad).setOnComplete(() =>
                {
                    scaleAnimationState = ScaleAnimationState.HasInBig;
                });
            });
        }

        public void ScaleToSmall()
        {
            if (scaleAnimationState != ScaleAnimationState.HasInBig)
            {
                return;
            }

            scaleAnimationState = ScaleAnimationState.ScaleToSmallAnimating;
            LeanTween.scale(gameObject, originalScale * 0.01f, 2f).setEase(LeanTweenType.easeInOutQuad)
                .setOnComplete(() =>
                {
                    scaleAnimationState = ScaleAnimationState.HasInSmall;
                });
        }
        
        public void FirstScaleToBig()
        {
            if (scaleAnimationState != ScaleAnimationState.HasInBig)
            {
                return;
            }

            if (!gameObject.activeInHierarchy)
            {
                return;
            }
            var cameraPosition = Camera.main.transform.position;
            var adjustedCameraPosition = new Vector3(cameraPosition.x, transform.position.y, cameraPosition.z);
            var distanceH = Vector3.Distance(transform.position, adjustedCameraPosition);
            
			//LeanTween.cancelAll();
            var currentScale = gameObject.transform.localScale;
            gameObject.transform.localScale = currentScale * 0.01f;
            scaleAnimationState = ScaleAnimationState.HasInSmall;
            Debug.Log("ScaleAnimationState.HasInSmall");
            if (distanceH < scaleToBigDistance)
            {
                Debug.Log("FirstScaleToBig + ScaleAnimationState.HasInSmall");
                LeanTween.scale(gameObject, originalScale, 2f)
                    .setEase(LeanTweenType.easeInOutQuad)
                    .setDelay(distanceH * 0.1f)
                    .setOnStart(() => 
                    {
                        Debug.Log("ScaleAnimationState.ScaleToBigAnimating");
                        scaleAnimationState = ScaleAnimationState.ScaleToBigAnimating;
                    })
                    .setOnComplete(() => 
                    {
                        Debug.Log("ScaleAnimationState.HasInBig");
                        scaleAnimationState = ScaleAnimationState.HasInBig;
                        isScaleAnimationCheckBegin = true;
                    });
            }
            else
            {
                Debug.Log("isScaleAnimationCheckBegin = true");
                isScaleAnimationCheckBegin = true;
            }
        }
    }
}