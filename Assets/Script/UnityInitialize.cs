using UnityEngine;
using UnityEngine.XR.ARFoundation;
using UnityImageLoader;
using EasyAR.Mega.Scene;

namespace Com.SceneConsole.Arte
{
    [DisallowMultipleComponent]
    public class UnityInitialize : MonoBehaviour
    {
        public GameObject easyARSession;
        public GameObject session;

        
        private GameObject easyARMegaBlocks;
        private MegaBlockSceneRoot[] megaModelManagers;
        private easyar.MegaBlockTrackerFrameFilter megaTracker;
        private easyar.MegaTrackerLocalizationStatus megaBlockLocalizationStatus;
        private int wakingUpCount;

        public OrientationManager manager;
        private PoiManager poi_manager;
        
        [HideInInspector]
        public bool sessionActive;
        [HideInInspector]
        public bool easyARSessionActive;

        private bool trackingStarted;
        private bool trackingInitial;
        private ARSessionState currentStatus = ARSessionState.None;
        private bool firstFound;

        public CreateViewer viewer;
        
        /// <summary>
        /// 新康兽
        /// </summary>
        public GameObject console;
        private bool isAnimating;
        private bool isConsoleHide;

        private float deltaTime; //控制通知有多个发布动作之间间隔
        private float _time;


        private void Awake()
        {
            LeanTween.init(1800);
            NativeAPI.UnityInitialize();
            Debug.Log("Unity Awake UnityInitialize ");
            ARSession.stateChanged += ARSessionOnStateChanged;
            easyARMegaBlocks = easyARSession.GetComponentInChildren<BlockHolder>(true).BlockRoot.gameObject;
            easyARSession.GetComponent<easyar.ARSession>().StateChanged += EasyARSessionOnStateChanged; 
            megaTracker = easyARSession.GetComponentInChildren<easyar.MegaBlockTrackerFrameFilter>(true);
            if (megaTracker)
            {
                megaTracker.LocalizationRespond += HandleLocalizationStatusChange;
            }
            megaModelManagers = easyARMegaBlocks.GetComponentsInChildren<MegaBlockSceneRoot>(true);
            foreach (var item in megaModelManagers)
            {
                item.SetCurrentMegaMapType(MegaMapType.h203);
            }
        }

        private void Start()
        {
            
            poi_manager = transform.GetComponentInChildren<PoiManager>(true);
            
        }

        private void Update()
        {
            deltaTime += Time.deltaTime;

            if (_time > deltaTime)
            {
                return;
            }

            
        }

        public void PushPublisher(ArtefactWarp warp)
        {
            // list.Add(warp);
        }
        public void SwitchEasyARMegaLocation(string n)
        {
            StopEasyARSession();
            if (n == "h203")
            {
                megaTracker.ServiceConfig.ServerAddress = "https://clsv3-api.easyar.com";
                megaTracker.ServiceConfig.AppID = "21a4370a4d3d4f4cadb340760ac41b0c";
                foreach (var item in megaModelManagers)
                {
                    item.SetCurrentMegaMapType(MegaMapType.h203);
                }
            } else if (n == "fanhaiB1110")
            {
                megaTracker.ServiceConfig.ServerAddress = "https://clsv3-api.easyar.com";
                megaTracker.ServiceConfig.AppID = "f8bad7281bf5457fa901d23c0b0ccaa0";
                foreach (var item in megaModelManagers)
                {
                    item.SetCurrentMegaMapType(MegaMapType.fanhaiB1110);
                }
            } else if (n == "xihu")
            {
                megaTracker.ServiceConfig.ServerAddress = "https://clsv3-api.easyar.com";
                megaTracker.ServiceConfig.AppID = "5a9a43baa7d840428b6efc37b97f5cc8";
                foreach (var item in megaModelManagers)
                {
                    item.SetCurrentMegaMapType(MegaMapType.xihu);
                }
                // Invoke(nameof(DelayTest), 1.5f);
            } else if (n == "street22")
            {
                megaTracker.ServiceConfig.ServerAddress = "https://clsv3-api.easyar.com";
                megaTracker.ServiceConfig.AppID = "9796d8aec5b849d19953e0f526819eae";//美术馆all
                foreach (var item in megaModelManagers)
                {
                    item.SetCurrentMegaMapType(MegaMapType.street22);
                }
            }
            megaTracker.ResetTracker();
            StartEasyARSession();
        }
        public void SetCurrentMegaActivity(string id)
        {
            Transform target = null;
            foreach (var item in megaModelManagers)
            {
                item.SetCurrentMegaActivity(id);
                var target0 = item.FindActivityDefaultNavTarget();
                if (target0)
                {
                    target = target0;
                }
            }

            if (target != null && id != "")
            {
                SwitchEasyARNavTarget(target);
            }
            else
            {
                SwitchEasyARNavTarget(null);
            }
        }
        
        public void SetEasyARMegaSkyDoomStatus(string setShow = "false")
        {
            var arg = bool.Parse(setShow);
            foreach (var item in megaModelManagers)
            {
                if (item.skyDoomGameObject)
                {
                    item.skyDoomGameObject.SetActive(arg);
                }
            }
            
        }

        // private void DelayTest()
        // {
        //     if (firstFound == false)
        //     {
        //         firstFound = true;
        //         MegaBlockScanAndShow();
        //     } else
        //     {
        //         SetMegaSceneActive(true);
        //     }
        // }

        public void StartEasyARSession()
        {
            Debug.Log("Unity StartEasyARSession " + easyARSessionActive);
            NativeAPI.EasyARSessionWillStart();
            session.SetActive(false);
            sessionActive = false;
            
            
            easyARMegaBlocks.SetActive(true);
            easyARMegaBlocks.GetComponentInChildren<LineRenderer>(true).positionCount = 0;
            SetMegaSceneActive(false);
            easyARSession.SetActive(true);
            easyARSession.GetComponent<easyar.ARSession>().enabled = true;
            easyARSession.GetComponent<easyar.ARSession>().StartSession();
            easyARSessionActive = true;
        }

        public void StopEasyARSession()
        {
            Debug.Log("Unity StopEasyARSession " + easyARSessionActive);
            easyARSession.GetComponent<easyar.ARSession>().StopSession(false);
            easyARSession.GetComponent<easyar.ARSession>().enabled = false;
            easyARSession.SetActive(false);
            easyARMegaBlocks.GetComponentInChildren<LineRenderer>(true).positionCount = 0;
            easyARMegaBlocks.SetActive(false);
            firstFound = false;
            easyARSessionActive = false;
            
            session.SetActive(true);
            sessionActive = true;
        }
        public void ClearEasyARNavTarget()
        { 
            GetComponentInChildren<ShowGoldenPath>().target = null;
            NativeAPI.NaviTargetChangeWithName("");
        }
        public void SwitchEasyARNavTarget(Transform target)
        { 
            // Debug.Log("SwitchEasyARNavTarget " + target.name);
            GetComponentInChildren<ShowGoldenPath>().target = target;
            if (target == null)
            {
                NativeAPI.NaviTargetChangeWithName("");
            }
            else
            {
                NativeAPI.NaviTargetChangeWithName(target.name+target.position.ToString());
            }
            
        }
        public void StartARSession()
        {
            Debug.Log("Unity StartARSession " + sessionActive);
            if (sessionActive)
            {
                return;
            }
            NativeAPI.SessionWillStart();
            sessionActive = true;
            trackingStarted = false;
            trackingInitial = false;
            currentStatus = ARSessionState.None;
            
            session.GetComponent<ARSession>().enabled = true;
            session.GetComponent<ARInputManager>().enabled = true;
            session.GetComponent<ARSession>().Reset();

        }

        public void StopARSession()
        {
            Debug.Log("Unity StopARSession " + sessionActive);
            if (!sessionActive)
            {
                return;
            }
            sessionActive = false;
            trackingStarted = false;
            trackingInitial = false;
            currentStatus = ARSessionState.None;
            
            session.GetComponent<ARSession>().enabled = false;
            session.GetComponent<ARInputManager>().enabled = false;
        }

        public void SetApplicationTargetFrameRate(string frameRate)
        {
            int frameRateToInt = int.Parse(frameRate);
            if (frameRateToInt > 60 || frameRateToInt < 0)
            {
                frameRateToInt = 60;
            }
            Application.targetFrameRate = frameRateToInt;
            Debug.Log("Unity 帧率设置为 +++++++=========== " + frameRateToInt);
        }

        public void ResetARSession()
        {

            if (!sessionActive)
            {
                Debug.Log("ResetARSession()!!!!!!!!!!!!!! sessionActive" );
                //session.GetComponent<ARSession>().enabled = true;
                //session.GetComponent<ARInputManager>().enabled = true;
                return;
            }
            else
            {
                Debug.Log("ResetARSession()!!!!!!!!!!!!!! sessionActive!!!!!!!!!!!else" );
                NativeAPI.SessionWillStart();
                trackingStarted = false;
                trackingInitial = false;
                currentStatus = ARSessionState.None;
                //arte_manager.ResetLocalArtesPosition();
                poi_manager.ResetLocalPoisPosition();
                //location_ad_manager.ResetLocationAdPosition();
                session.GetComponent<ARSession>().Reset();
            }
            sessionActive = true;

        }

        public void ContinueOrientationUpdate()
        {
            manager.ContinueUpdate();
            //poi_orientation_manager.ContinueUpdate();
        }

        public void ResetOrientationMaxUpdates()
        {
            manager.ResetMaxUpdates();
            //poi_orientation_manager.ResetMaxUpdates();
        }

        public void ResetOrientation()
        {
            manager.Restart();
            //poi_orientation_manager.Restart();
        }

        public void StartARLocation()
        {
            //GameObject locationRoot = transform.Find("ARLocationRoot").gameObject;
            //locationRoot.SetActive(true);
        }

        public void ActivateCreateViewer()
        {
            viewer.gameObject.SetActive(true);
        }

        public void DeActivateCreateViewer()
        {
            viewer.gameObject.SetActive(false);
        }

        public void SetCreateViewerMask(string img)
        {
            viewer.SetMask(img);
        }

        public void HideOrShowSceneconsole()
        {
            if (isAnimating)
            {
                return;
            }
            isAnimating = true;
            if (!isConsoleHide)
            {
                isConsoleHide = true;
                LeanTween.moveLocal(console, new Vector3(200f, 0f, 200f), 0.6f).setEase(LeanTweenType.easeInCubic).setOnComplete(() =>
                {
                    isAnimating = false;
                });
            }
            else
            {
                isConsoleHide = false;
                LeanTween.moveLocal(console, new Vector3(0f, 0f, 5f), 0.6f).setEase(LeanTweenType.easeOutCubic).setOnComplete(() =>
                {
                    isAnimating = false;
                });
            }
        }

        public void ClearImageCache()
        {
            ImageLoader.GetInstance().RemoveCache();
        }

        private void ARSessionOnStateChanged(ARSessionStateChangedEventArgs args)
        {
#if DEBUG
            Debug.Log(args.ToString());
#endif
            if (trackingStarted)
            {
                if (args.state == ARSessionState.SessionTracking)
                {
                    if (currentStatus != ARSessionState.SessionTracking)
                    {
#if DEBUG
                        Debug.Log("Tracking Restored!");
#endif
                    }
                }
                else if (currentStatus == ARSessionState.SessionTracking)
                {
#if DEBUG
                    Debug.Log("Tracking Lost!");
#endif
                }

                currentStatus = args.state;
            }
            else
            {
                if (args.state == ARSessionState.SessionInitializing)
                {
                    trackingInitial = true;
                    //console.GetComponent<MeshRenderer>().enabled = true;
                    currentStatus = args.state;
                    NativeAPI.SessionInitializing();
                }
                else if (args.state == ARSessionState.SessionTracking && currentStatus == ARSessionState.SessionInitializing)
                {
                    trackingStarted = true;
                    NativeAPI.SessionDidStarted();
#if DEBUG
                    Debug.Log("Tracking Started!");
#endif
                }
            }
            //var reason = ARSession.notTrackingReason;
            //Debug.Log("[unity]args.state" + args.state);
            //Debug.Log("[unity]notTrackingReason" + reason);
        }

        private void EasyARSessionOnStateChanged(easyar.ARSession.SessionState args)
        {
#if DEBUG
            Debug.Log(args.ToString());
#endif
            if (args == easyar.ARSession.SessionState.Broken || args == easyar.ARSession.SessionState.None || args == easyar.ARSession.SessionState.Paused)
            {

            } else if (args == easyar.ARSession.SessionState.Assembling || args == easyar.ARSession.SessionState.Assembled || args == easyar.ARSession.SessionState.Ready)
            {

            } else if (args == easyar.ARSession.SessionState.Running)
            {
                NativeAPI.EasyARSessionDidStarted();
            }
            
        }

        private void HandleLocalizationStatusChange(easyar.MegaBlockLocalizationResponse response)
        {
            NativeAPI.EasyARMegaBlockLocalizationStatusChanged((int)response.Status);
            megaBlockLocalizationStatus = response.Status;
            var status = response.Status;
            wakingUpCount = status == easyar.MegaTrackerLocalizationStatus.WakingUp ? wakingUpCount + 1 : 0;
            if (wakingUpCount >= 5)
            {
                Debug.Log(
                    "Service is waking up, please wait patiently." + System.Environment.NewLine +
                    "服务正在唤醒中，请耐心等待。"
                );
            }

            if (status == easyar.MegaTrackerLocalizationStatus.QpsLimitExceeded)
            {
                Debug.Log("Too many users, please wait patiently." + System.Environment.NewLine +
                    "用户过多，请耐心等待。"
                );
            }

            if (status == easyar.MegaTrackerLocalizationStatus.Found)
            {
                // print("easyar.MegaTrackerLocalizationStatus.Found");
                if (firstFound == false)
                {
                    firstFound = true;
                    MegaBlockScanAndShow();
                }
            } else if (status == easyar.MegaTrackerLocalizationStatus.NotFound)
            {
                // // For Test;测试用；
                // firstFound = false;
                // SetMegaSceneActive(false);
            }
        }
        public void ShowMegaModelsScaleToBigAnimation()
        {
            MegaBlockScanAndShow();
        }
        // private void ShowMegaModelsWhenFound()
        // {
        //     // Debug.Log("延迟执行的函数");
        //     // Debug.Log(Camera.main.transform.position);
        //     // if (megaBlockLocalizationStatus == easyar.MegaTrackerLocalizationStatus.Found)
        //     // {
        //         foreach (var item in megaModelManagers)
        //         {
        //             item.ShowMegaModelsAnimation();
        //         }
        //     // }
        //     
        // }
        private void MegaBlockScanAndShow()
        {
            foreach (var item in megaModelManagers)
            {
                item.ScanAndShow();
            }
            // Invoke(nameof(ShowMegaModelsWhenFound), 1.5f);
        }
        private void SetMegaSceneActive(bool active)
        {
            foreach (var item in megaModelManagers)
            {
                item.SetMegaSceneActive(active);
            }
        }
    }
}
