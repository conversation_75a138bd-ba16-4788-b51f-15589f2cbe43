using UnityEngine;

namespace Com.SceneConsole.Arte
{
    /// <summary>
    /// 聚焦检测
    /// </summary>
    public class RaycastFocus : MonoBehaviour
    {
        public Camera mainCamera;
        public Vector2 raycastScreenPoint = Vector2.negativeInfinity;

        private GameObject warp;
        private UnityInitialize session;
        private UnityMegaMapManager megaMapManager;
        
        private float deltaTime;
        private float lastTime;

        private bool isActive = true;
        private bool focusEnable = true;
        
        private HittestNaviTarget hittestNaviTarget;
        
        /// <summary>
        /// 开启射线检测
        /// </summary>
        public void OpenRaycast()
        {
            isActive = true;
        }

        /// <summary>
        /// 关闭射线检测
        /// </summary>
        public void CloseRaycast()
        {
            isActive = false;
        }

        /// <summary>
        /// 开启聚焦
        /// </summary>
        public void OpenFocus()
        {
            focusEnable = true;
        }

        public void CloseFocus()
        {
            focusEnable = false;
        }
        public void SetRaycastScreenPoint(string point)
        {
            var array = point.Split(',');
            raycastScreenPoint = new Vector2(float.Parse(array[0]), float.Parse(array[1]));
        }

        public void SwitchContentVisible(string visible)
        {
            // contentVisible = bool.Parse(visible);
        }

        public void StartNavToCurrentHittestTarget()
        {
            if (hittestNaviTarget)
            {
                megaMapManager.SwitchEasyARNavTarget(hittestNaviTarget.target);
            }
        }
        public void TapScreenWithPoint(string point)
        {
            Debug.Log("!!!!!!!!!!!!RaycastFocus");
            var array = point.Split(',');
            Vector2 vector = new Vector2(float.Parse(array[0]), float.Parse(array[1]));
            Ray rayCast = mainCamera.ScreenPointToRay(vector);
            Physics.queriesHitBackfaces = true;
            if (Physics.Raycast(rayCast, out RaycastHit castHit))
            {
                Debug.Log("!!!!!!!!!!!!Physics.Raycast(rayCast, out Ray");
                Transform hitTest = castHit.transform;
                if (CameraManager.Instance.Mode == CameraManager.CameraMode.normal)
                {
                    if (hitTest != null)
                    {
                        // if (hitTest.parent.CompareTag(FoodMapPlaneNode.Tag) || hitTest.CompareTag(FoodMapPlaneNode.Tag))
                        // {
                        //     Debug.Log("!!!!!!!!!!!!if (hitTest.parent.CompareTag"+hitTest.name);
                        //     FoodMapPlaneNode node = hitTest.transform.parent.GetComponentInChildren<FoodMapPlaneNode>();
                        //     node.OnNavAction = () =>
                        //     {
                        //         session.SwitchEasyARNavTarget(node.target);
                        //     };
                        //     node.Tapped(hitTest);
                        // }
                        // else if (hitTest.CompareTag(GuideboardPlaneNode.Tag))
                        if (hitTest.GetComponent<GuideboardPlaneNode>())
                        {
                            if (hitTest.GetComponent<GuideboardPlaneNode>().isNavTarget)
                            {
                                megaMapManager.SwitchEasyARNavTarget(hitTest);
                            }
                        }
                        else if (hitTest.GetComponent<HittestNaviTarget>())
                        {
                            hittestNaviTarget = hitTest.GetComponent<HittestNaviTarget>();
                            var hasARTarget = hittestNaviTarget.target == null ? 0 : 1;
                            NativeAPI.OnHittestWithName(hittestNaviTarget.targetName, (int)hittestNaviTarget.mainType, hasARTarget);
                            Debug.Log(hittestNaviTarget.targetName + "hasARTarget" + hasARTarget);
                        }
                        else
                        {
                            megaMapManager.SwitchEasyARNavTarget(hitTest);
                        }
                    }
                    else
                    {
                        // session.SwitchEasyARNavTarget(null);
                    }
                }
                
                // Debug.Log("!!!!!!!22222!!!!!Physics.Raycast(rayCast, out Ray");
                Debug.Log(CameraManager.Instance.Mode);
                // Debug.Log("!!!!!!!22222!!!!!");
                if (CameraManager.Instance.Mode == CameraManager.CameraMode.poi)
                {
                    // Debug.Log("!!!!!!!!!!!!if (hitTest.CompareTag");

                    if (hitTest.CompareTag("Poi"))
                    {
                        PoiPlaneNode node = hitTest.transform.GetComponent<PoiPlaneNode>();
                        Debug.Log("!!!!!!!!!!!!123454322322if (hitTest.CompareTag");
                        NativeAPI.didTapPoi(node.Model.id);
                        Debug.Log(node.Model.id);
                        //node.PlayOrPause();
                    }
                }
            }
            else
            {
                // session.SwitchEasyARNavTarget(null);
            }
        }

        private void Start()
        {
            session = transform.GetComponent<UnityInitialize>();
            megaMapManager = transform.GetComponent<UnityMegaMapManager>();
        }

        void Update()
        {
            deltaTime += Time.deltaTime;
            if (!isActive || !(session.sessionActive || session.easyARSessionActive))
            {
                if (warp != null)
                {
                    if (deltaTime - lastTime > 1)
                    {
                        if (warp.GetComponent<GuideboardPlaneNode>())
                        {
                            warp.GetComponent<GuideboardPlaneNode>().StopPreview();
                        }
                        warp = null;
                        NativeAPI.OnFocusUpdated("",0);
                    }
                }
                return;
            }
            if (CameraManager.Instance.Mode != CameraManager.CameraMode.normal)
            {
                return;
            }
            
            if (deltaTime - lastTime > 1)
            {
                GameObject newTarget = null;
                Ray ray;
                if (raycastScreenPoint != Vector2.negativeInfinity)
                {
                    ray = mainCamera.ScreenPointToRay(raycastScreenPoint);
                }
                else
                {
                    ray = new Ray(mainCamera.transform.position, mainCamera.transform.forward);
                }
                 
                if (Physics.Raycast(ray, out RaycastHit raycastHit))
                {
                    GameObject hitTest = raycastHit.transform.gameObject;
                    newTarget = hitTest;
                }
                if (newTarget != null)
                {
                    var newTargetId = newTarget.name + newTarget.transform.position.ToString();
                    if (warp != null)
                    {
                        var warpId = warp.name + warp.transform.position.ToString();
                        if (newTargetId != warpId)
                        {
                            if (warp.GetComponent<GuideboardPlaneNode>())
                            {
                                warp.GetComponent<GuideboardPlaneNode>().StopPreview();
                            }
                            if (focusEnable)
                            {
                                warp = newTarget;
                                lastTime = deltaTime;
                                if (warp.GetComponent<GuideboardPlaneNode>())
                                {
                                    warp.GetComponent<GuideboardPlaneNode>().StartPreview();
                                }
                                var modelType = GameObjectModelTypeHolder.GameObjectModelType.None;
                                if (warp.GetComponent<GameObjectModelTypeHolder>())
                                {
                                    modelType = warp.GetComponent<GameObjectModelTypeHolder>().gameObjectModelType;
                                }
                                else if (warp.transform.parent.GetComponent<GameObjectModelTypeHolder>() && !(warp.transform.parent.GetComponent<Collider>()))
                                {
                                    modelType = warp.transform.parent.GetComponent<GameObjectModelTypeHolder>().gameObjectModelType;
                                }
                                Debug.Log("modelType1:"+modelType+(int)modelType);
                                NativeAPI.OnFocusUpdated(warpId,(int)modelType);
                            }
                        }
                    }
                    else
                    {
                        if (focusEnable)
                        {
                            warp = newTarget;
                            lastTime = deltaTime;
                            if (warp.GetComponent<GuideboardPlaneNode>())
                            {
                                warp.GetComponent<GuideboardPlaneNode>().StartPreview();
                            }
                            var modelType = GameObjectModelTypeHolder.GameObjectModelType.None;
                            if (warp.GetComponent<GameObjectModelTypeHolder>())
                            {
                                modelType = warp.GetComponent<GameObjectModelTypeHolder>().gameObjectModelType;
                            }
                            else if (warp.transform.parent.GetComponent<GameObjectModelTypeHolder>() && !(warp.transform.parent.GetComponent<Collider>()))
                            {
                                modelType = warp.transform.parent.GetComponent<GameObjectModelTypeHolder>().gameObjectModelType;
                            }
                            Debug.Log("modelType2:"+modelType+(int)modelType);
                            NativeAPI.OnFocusUpdated(newTargetId,(int)modelType);
                        }
                    }
                }
                else
                {
                    if (warp != null)
                    {
                        if (warp.GetComponent<GuideboardPlaneNode>())
                        {
                            warp.GetComponent<GuideboardPlaneNode>().StopPreview();
                        }
                        warp = null;
                        NativeAPI.OnFocusUpdated("",0);
                    }
                }
            }
        }
        
        public void RotationAddDegreeWithAnimation(string degreeAndDuration)
        {
            var array = degreeAndDuration.Split(',');
            var degree = float.Parse(array[0]);
            var rotationDuration = float.Parse(array[1]);
            if (warp.GetComponent<AutoRotation>() && warp.GetComponent<GameObjectModelTypeHolder>())
            {
                if (warp.GetComponent<GameObjectModelTypeHolder>().gameObjectModelType ==
                    GameObjectModelTypeHolder.GameObjectModelType.SpiritualFortress)
                {
                    warp.GetComponent<AutoRotation>().RotationAddDegreeWithAnimation(degree, rotationDuration);
                }
            }
        }
    
        public void RotationToDegreeWithAnimation(string degreeAndDuration)
        {
            var array = degreeAndDuration.Split(',');
            var degree = float.Parse(array[0]);
            var rotationDuration = float.Parse(array[1]);
            if (warp.GetComponent<AutoRotation>() && warp.GetComponent<GameObjectModelTypeHolder>())
            {
                if (warp.GetComponent<GameObjectModelTypeHolder>().gameObjectModelType ==
                    GameObjectModelTypeHolder.GameObjectModelType.SpiritualFortress)
                {
                    warp.GetComponent<AutoRotation>().RotationToDegreeWithAnimation(degree, rotationDuration);
                }
            }
        }

        public void ResetRotation(float duration)
        {
            if (duration > 0)
            {
                LeanTween.rotateY(warp, 0, duration);
            }
            else
            {
                warp.transform.localRotation = Quaternion.identity;
            }
        
        }

    }
}