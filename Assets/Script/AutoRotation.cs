using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class AutoRotation : MonoBehaviour
{
    // Start is called before the first frame update
    public float autoRotationSpeed = 1.0f;//-0.35
    public Vector3 autoRotationAxis = Vector3.up;
    public bool autoRotate = true;
    
    void Start()
    {
        
    }

    // Update is called once per frame
    void Update()
    {
        if (!autoRotate)
        {
            return;
        }
        
        Quaternion rotU = Quaternion.AngleAxis(100 * Time.deltaTime * autoRotationSpeed, Vector3.up);
        transform.localRotation = transform.localRotation * rotU;
    }

    public void RotationAddDegreeWithAnimation(float degree, float rotationDuration)
    {
        LeanTween.rotateAroundLocal(gameObject, Vector3.up, degree, rotationDuration);
    }
    
    public void RotationToDegreeWithAnimation(float degree, float rotationDuration)
    {
        LeanTween.rotateY(gameObject, degree, rotationDuration);
    }

    public void ResetRotation(float duration)
    {
        if (duration > 0)
        {
            LeanTween.rotateY(gameObject, 0, duration);
        }
        else
        {
            transform.localRotation = Quaternion.identity;
        }
        
    }
}
