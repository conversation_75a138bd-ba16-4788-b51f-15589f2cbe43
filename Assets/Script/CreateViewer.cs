using System.Collections;
using System.Collections.Generic;

using UnityEngine;
using UnityEngine.UI;
using UnityEngine.XR.ARFoundation;

namespace Com.SceneConsole.Arte
{
    public class CreateViewer : MonoBehaviour
    {
        public ARCameraBackground cameraBg;
        public RenderTexture renderTexture;

        private RawImage rawImg;

        public void SetAlpha(float val)
        {

            transform.GetComponent<RawImage>().material.SetFloat("_Alpha", val);

        }

        public void SetMask(string img)
        {
            var texture = ArtefactWarp.Base64StringToTexture2D(img);
            transform.GetComponent<RawImage>().material.SetTexture("_MaskTex", texture);
        }

        private void Start()
        {
            rawImg = GetComponent<RawImage>();
            rawImg.texture = renderTexture;
            rawImg.material.mainTexture = renderTexture;

        }

        private void Update()
        {
            Graphics.Blit(null, renderTexture, cameraBg.material);
        }
    }
}
