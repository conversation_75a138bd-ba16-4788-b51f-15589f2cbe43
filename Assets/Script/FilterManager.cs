using System.Collections;
using System.Collections.Generic;

using UnityEngine;

namespace Com.SceneConsole.Arte
{
    [DisallowMultipleComponent]
    public class FilterManager : Singleton<FilterManager>
    {
        [HideInInspector]
        public string filterID = "0";

        private Component filter;
        private CameraFilterPack_Colors_Brightness brightness;

        /// <summary>
        /// 切换摄像机滤镜
        /// </summary>
        /// <param name="filter"></param>
        public void SwitchCameraFilter(string id)
        {
            //CameraFilterPack_TV_Artefact
            //CameraFilterPack_TV_Horror 黑色
            //CameraFilterPack_TV_Noise 杂色--
            //CameraFilterPack_Pixelisation_OilPaintHQ 油画
            //CameraFilterPack_Sharpen_Sharpen 漫画--
            //CameraFilterPack_TV_ARCADE 老电视
            //CameraFilterPack_Oculus_NightVision2 夜视仪
            //CameraFilterPack_Edge_Sigmoid 素描--
            //CameraFilterPack_Edge_Sobel 黑色素描
            //CameraFilterPack_Drawing_Paper 蓝色素描
            //CameraFilterPack_Drawing_Toon 卡通
            //CameraFilterPack_Drawing_BluePrint 蓝图
            //CameraFilterPack_Atmosphere_Rain_Pro_3D 下雨
            //CameraFilterPack_3D_Snow 下雪

            this.filterID = id;

            if (filter != null)
            {
                Destroy(filter);
            }

            var filterID = int.Parse(id);
            if (filterID > 0)
            {
                if (filterID == 1)
                {
                    CameraFilterPack_Colors_Adjust_PreFilters adjust = transform.gameObject.AddComponent<CameraFilterPack_Colors_Adjust_PreFilters>();
                    adjust.filterchoice = CameraFilterPack_Colors_Adjust_PreFilters.filters.BlackAndWhite_Orange;
                    filter = adjust;
                }
                if (filterID == 2)
                {
                    filter = transform.gameObject.AddComponent<CameraFilterPack_TV_Artefact>();
                }
                if (filterID == 3)
                {
                    filter = transform.gameObject.AddComponent<CameraFilterPack_TV_Horror>();
                }
                if (filterID == 4)
                {
                    filter = transform.gameObject.AddComponent<CameraFilterPack_Glow_Glow>();
                }
                if (filterID == 5)
                {
                    filter = transform.gameObject.AddComponent<CameraFilterPack_Color_GrayScale>();
                }
                if (filterID == 6)
                {
                    filter = transform.gameObject.AddComponent<CameraFilterPack_Drawing_Manga_Color>();
                }
                if (filterID == 7)
                {
                    CameraFilterPack_Colors_Adjust_PreFilters adjust = transform.gameObject.AddComponent<CameraFilterPack_Colors_Adjust_PreFilters>();
                    adjust.filterchoice = CameraFilterPack_Colors_Adjust_PreFilters.filters.NightVision;
                    filter = adjust;
                }
                if (filterID == 8)
                {
                    filter = transform.gameObject.AddComponent<CameraFilterPack_Edge_Sobel>();
                }
                if (filterID == 9)
                {
                    filter = transform.gameObject.AddComponent<CameraFilterPack_Pixelisation_OilPaint>();
                }
                if (filterID == 10)
                {
                    CameraFilterPack_Atmosphere_Rain rain = transform.gameObject.AddComponent<CameraFilterPack_Atmosphere_Rain>();
                    rain.Intensity = 0.1f;
                    rain.Size = 0.4f;
                    rain.Speed = 0.05f;
                    filter = rain;
                }
                if (filterID == 11)
                {
                    filter = transform.gameObject.AddComponent<CameraFilterPack_Drawing_BluePrint>();
                }
                if (filterID == 12)
                {
                    filter = transform.gameObject.AddComponent<CameraFilterPack_Blur_Steam>();
                }
                if (filterID == 13)
                {
                    filter = transform.gameObject.AddComponent<CameraFilterPack_TV_ARCADE_Fast>();
                }
                if (filterID == 14)
                {
                    filter = transform.gameObject.AddComponent<CameraFilterPack_Drawing_Toon>();
                }
            }
        }

        /// <summary>
        /// 绘画时添加亮度减低滤镜
        /// </summary>
        /// <param name="add"></param>
        public void SwitchPaintDarkFilter(bool dark)
        {
            if (dark)
            {
                if (brightness == null)
                {
                    brightness = gameObject.AddComponent<CameraFilterPack_Colors_Brightness>();
                    brightness._Brightness = 1.0f;
                    LeanTween.value(gameObject, 1f, 0.7f, 1f).setOnUpdate((float val) =>
                    {
                        brightness._Brightness = val;
                    });
                }
            }
            else
            {
                if (brightness != null)
                {
                    LeanTween.value(gameObject, brightness._Brightness, 1.0f, 1f).setOnUpdate((float val) =>
                    {
                        brightness._Brightness = val;
                    }).setOnComplete(() =>
                    {
                        Destroy(brightness);
                    });
                }
            }
        }
    }
}
