using System;
using System.Collections;
using System.Collections.Generic;

using UnityEngine;
using UnityEngine.UI;

namespace Com.SceneConsole.Arte
{

    public class FixedAdManager : MonoBehaviour
    {
        public DetailImageNode imgPrefab;
        public DetailVideoNode videoPrefab;
        public DetailAudioNode audioPrefab;

        private int playIndex;
        // Start is called before the first frame update
        void Start()
        {

        }

        // Update is called once per frame
        void Update()
        {

        }
        public void ShowAdItemsAnimated(string json)
        {
            ArtefactDetailItem[] items = JsonHelper.FromJson<ArtefactDetailItem>(json);
            AddAdItems(items, true);
        }
        public void ShowAdItems(string json)
        {
            ArtefactDetailItem[] items = JsonHelper.FromJson<ArtefactDetailItem>(json);
            AddAdItems(items, false);
        }

        private void AddAdItems(ArtefactDetailItem[] items, bool animated)
        {
            HideAdItems();
            transform.position = Camera.main.transform.position;
            transform.rotation = Quaternion.AngleAxis(Camera.main.transform.eulerAngles.y, Vector3.up);
            List<Vector3> points = PointOnSector(items.Length);
            for (int i = 0; i < points.Count; i++)
            {
                Vector3 point = points[i];
                ArtefactDetailItem item = items[i];
                //Debug.Log(item);

                if (item.mt == "image")
                {
                    DetailImageNode node = Instantiate(imgPrefab, point, Quaternion.identity);
                    ChangeLayer(node.transform, LayerMask.NameToLayer("FixedAd"));
                    //node.transform.gameObject.layer = LayerMask.NameToLayer("Poi");
                    node.name = "ImageNode" + i;
                    node.Model = item;
                    node.transform.SetParent(transform);
                    if (animated)
                    {
                        node.transform.localPosition = point * 0.01f;
                        LeanTween.moveLocal(node.gameObject, point, 1.2f);
                    }
                    else
                    {
                        node.transform.localPosition = point;
                    }

                    node.transform.LookAt(new Vector3(transform.position.x, node.transform.position.y, transform.position.z), Vector3.up);
                }
                else if (item.mt == "video")
                {
                    DetailVideoNode node = Instantiate(videoPrefab, point, Quaternion.identity);
                    ChangeLayer(node.transform, LayerMask.NameToLayer("FixedAd"));
                    node.name = "VideoNode" + i;
                    node.Model = item;
                    node.transform.SetParent(transform);
                    if (animated)
                    {
                        node.transform.localPosition = point * 0.01f;
                        LeanTween.moveLocal(node.gameObject, point, 1.2f);
                    }
                    else
                    {
                        node.transform.localPosition = point;
                    }
                    node.transform.LookAt(new Vector3(transform.position.x, node.transform.position.y, transform.position.z), Vector3.up);
                    //Destroy(node.canvas.GetComponentInChildren<RawImage>().texture);
                    
                }
                else if (item.mt == "audio")
                {
                    DetailAudioNode node = Instantiate(audioPrefab, point, Quaternion.identity);
                    ChangeLayer(node.transform, LayerMask.NameToLayer("FixedAd"));
                    node.name = "AudioNode" + i;
                    node.Model = item;
                    node.transform.SetParent(transform);
                    if (animated)
                    {
                        node.transform.localPosition = point * 0.01f;
                        LeanTween.moveLocal(node.gameObject, point, 1.2f);
                    }
                    else
                    {
                        node.transform.localPosition = point;
                    }
                    node.transform.LookAt(new Vector3(transform.position.x, node.transform.position.y, transform.position.z), Vector3.up);
                }

            }
        }

        public void HideAdItems()
        {
            int childCount = transform.childCount;
            for (int j = 0; j < childCount; j++)
            {
                Destroy(transform.GetChild(j).gameObject);
                Transform canvas = transform.GetChild(j).transform.Find("Canvas");
                if (canvas != null) {
                    Destroy(canvas.GetComponentInChildren<RawImage>().texture);
                }
                
            }
            playIndex = -1;
        }

        public void OnAudioPlaying(string time)
        {
            int childCount = transform.childCount;
            for (int j = 0; j < childCount; j++)
            {
                Transform tf = transform.GetChild(j);
                if (tf.name.Contains("Audio"))
                {
                    DetailAudioNode node = tf.GetComponent<DetailAudioNode>();
                    if (node.isPlaying)
                    {
                        node.time = float.Parse(time);
                    }
                }
            }
        }

        public void OnAudioStatusUpdate(string play)
        {
            if (CameraManager.Instance.Mode != CameraManager.CameraMode.poi)
            {
                return;
            }
            if (playIndex != -1 && playIndex < transform.childCount)
            {
                Transform tf = transform.GetChild(playIndex);
                if (tf.name.Contains("Audio"))
                {
                    bool value = bool.Parse(play);
                    DetailAudioNode node = tf.GetComponent<DetailAudioNode>();
                    node.UpdateStatus(value);
                }
            }
        }

        public void DidStartPlay(string name, bool audio = false)
        {
            NativeAPI.WillPlayMedia();
            playIndex = -1;
            int childCount = transform.childCount;
            for (int j = 0; j < childCount; j++)
            {
                Transform tf = transform.GetChild(j);
                if (tf.name != name)
                {
                    if (tf.name.Contains("Video"))
                    {
                        DetailVideoNode node = tf.GetComponent<DetailVideoNode>();
                        node.Stop();
                    }
                    if (tf.name.Contains("Audio"))
                    {
                        DetailAudioNode node = tf.GetComponent<DetailAudioNode>();
                        if (node.isPlaying)
                        {
                            if (audio)
                            {
                                node.StopPlay();
                            }
                            else
                            {
                                node.PlayOrPause();
                            }
                        }
                    }
                }
                else
                {
                    playIndex = j;
                }

            }
        }
        public void StopAllItemsPlay()
        {
            playIndex = -1;
            int childCount = transform.childCount;
            for (int j = 0; j < childCount; j++)
            {
                Transform tf = transform.GetChild(j);

                if (tf.name.Contains("Video"))
                {
                    DetailVideoNode node = tf.GetComponent<DetailVideoNode>();
                    node.Stop();
                }
                if (tf.name.Contains("Audio"))
                {
                    DetailAudioNode node = tf.GetComponent<DetailAudioNode>();
                    if (node.isPlaying)
                    {
                        node.StopPlay();

                    }
                }
            }
        }
        private void ChangeLayer(Transform transform, int layer)
        {
            if (transform.childCount > 0)
            {
                for (int i = 0; i < transform.childCount; i++)
                {
                    ChangeLayer(transform.GetChild(i), layer);
                }
            }
            else
            {
                transform.gameObject.layer = layer;
            }
        }
        private List<Vector3> PointOnSector(int count, double radius = 1.5)
        {
            var points = new List<Vector3>();
            float angle = -Mathf.PI / 2;
            //float angle = Mathf.PI / 4;
            float height = 0;
            if (count > 4)
            {
                height = -0.6f;
            }
            for (int i = 0; i < count; i++)
            {

                int x;
                float h = 0;
                int j = i % 5;
                if (j % 2 == 0)
                {
                    x = -j / 2;
                }
                else
                {
                    x = (j + 1) / 2;
                }
                var ang = angle * (double)i;
                if (i > 9)
                {
                    h += height + 3.2f;
                }
                else if (i > 4)
                {
                    h += height + 1.8f;
                }
                Vector3 location = new Vector3((float)(Math.Sin(ang) * radius), h, (float)(Math.Cos(ang) * radius));
                points.Add(location);
            }
            return points;
        }
    }
}