using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;

using UnityEngine;

namespace Com.SceneConsole.Arte
{

    public class LocationAdManager : MonoBehaviour
    {
        public DetailImageNode imgPrefab;
        public DetailVideoNode videoPrefab;
        public DetailAudioNode audioPrefab;
        /// <summary>
        /// 摄像机
        /// </summary>
        public Camera mainCamera;

        [HideInInspector]
        public bool layouting = false;
        private Vector3 layoutPosition = Vector3.zero;
        private readonly HashSet<DetailImageNode> warpList = new HashSet<DetailImageNode>();
        private string playId;
        // Start is called before the first frame update
        void Start()
        {

        }

        // Update is called once per frame
        void Update()
        {

        }

        void LateUpdate()
        {
            var cameraP = Camera.main.transform.position;
            foreach (var item in warpList)
            {
                var cameraAdjustP = new Vector3(cameraP.x, item.transform.position.y, cameraP.z);
                var targetP = cameraAdjustP;//item.transform.position * 2 - cameraAdjustP;
                item.transform.LookAt(targetP, Vector3.up);
            }
        }
        public void ShowAdItems()
        {
            CameraManager.Instance.Mode = CameraManager.CameraMode.normal;

        }
        public void RemoveAllAdItems()
        {
            foreach (var item in warpList)
            {
                Destroy(item.gameObject);
            }

            playId = null;
            warpList.Clear();
        }

        /// <summary>
        /// 更新LocationAd的位置 大小
        /// </summary>
        /// <param name="json"></param>
        public void LayoutLocationAd(string json)
        {
#if DEBUG
            Debug.Log(json);
#endif
            Layoutor layoutor = JsonUtility.FromJson<Layoutor>(json);
            LayoutDidUpdate(layoutor.layouts, layoutor.animated);

        }
        /// <summary>
        /// 更新位置大小
        /// </summary>
        private void LayoutDidUpdate(ArtefactLayout[] layouts, bool animated)
        {
            layoutPosition = mainCamera.transform.position;
            foreach (var layout in layouts)
            {
                DetailImageNode match = FindInList(layout.id);
                //Debug.Log(match);
                if (match != null)
                {

                    match.transform.parent = transform;
                    var reletiveVector = transform.right * layout.x + transform.forward * layout.y;
                    Vector3 vector = mainCamera.transform.position + reletiveVector;
                    vector.y = 0 + layout.height + mainCamera.transform.position.y - 1.5f;

                    if (animated)
                    {
                        LeanTween.move(match.gameObject, vector, 1.2f);
                        LeanTween.value(match.gameObject, match.Scale, layout.scale * 0.3f, 1.0f).setEase(LeanTweenType.easeOutCubic).setOnUpdate((float val) =>
                        {
                            match.Scale = val;
                        });

                    }
                    else
                    {
                        match.transform.position = vector;
                        match.Scale = layout.scale * 0.3f;
                    }
                    //Debug.Log(vector.ToString());
                }
                else
                {
#if DEBUG
                    Debug.Log(layout.id);
#endif

                }
            }
            layouting = true;
            LeanTween.delayedCall(2.0f, () =>
            {
                layouting = false;
            });
        }

        /// <summary>
        /// 重置LocationAd的位置
        /// </summary>
        public void ResetLocationAdPosition()
        {
            layoutPosition = Vector3.zero;
            var tf = Camera.main.transform;
            foreach (var item in warpList)
            {
                if (item.transform.position != Vector3.zero)
                {
                    var trans = tf.InverseTransformPoint(item.transform.position);
                    item.transform.position = trans;
                }
            }
        }

        /// <summary>
        /// 添加LocationAd
        /// </summary>
        /// <param name="json"></param>
        public void AddLocationAds(string json)
        {
#if DEBUG
            Debug.Log(json);
#endif
            ArtefactDetailItem[] list = JsonHelper.FromJson<ArtefactDetailItem>(json);
            for (int i = 0; i < list.Length; i++)
            {
                var model = list[i];
                CreateLocationAds(model);
            }
        }

        /// <summary>
        /// 移除LocationAd
        /// </summary>
        /// <param name="json"></param>
        public void RemoveLocationAds(string json)
        {
#if DEBUG
            Debug.Log(json);
#endif
            string[] list = JsonHelper.FromJson<string>(json);

            var artis = FindSetInList(list);
            foreach (var item in artis)
            {
                warpList.Remove(item);
                Destroy(item.gameObject);
            }
        }

        /// <summary>
        /// DetailImageNode
        /// </summary>
        /// <param name="LocationAd"></param>
        /// <returns></returns>
        private DetailImageNode CreateLocationAds(ArtefactDetailItem item)
        {
            Vector3 targetPos = mainCamera.transform.position;
            targetPos.y += 2;

            DetailImageNode warp = null;
            if (item.mt == "image")
            {
                DetailImageNode node = Instantiate(imgPrefab, Vector3.zero, Quaternion.identity);
                ChangeLayer(node.transform, LayerMask.NameToLayer("LocationAd"));
                node.name = "ImageNode" + item.id;
                warp = node;
            }
            else if (item.mt == "video")
            {
                DetailVideoNode node = Instantiate(videoPrefab, Vector3.zero, Quaternion.identity);
                ChangeLayer(node.transform, LayerMask.NameToLayer("LocationAd"));
                node.name = "VideoNode" + item.id;
                warp = node;
            }
            else if (item.mt == "audio")
            {
                DetailAudioNode node = Instantiate(audioPrefab, Vector3.zero, Quaternion.identity);
                ChangeLayer(node.transform, LayerMask.NameToLayer("LocationAd"));
                node.name = "AudioNode" + item.id;
                warp = node;
            }
            warp.Model = item;
            //Debug.Log(warp);
            //判断是否添加成功
            if (warpList.Add(warp))
            {
                warp.transform.SetParent(transform);
                return warp;
            }
            else
            {
                Destroy(warp.gameObject);
            }

            return null;
        }

        private DetailImageNode FindInList(string id)
        {
            foreach (var item in warpList)
            {
                if (item.Model.id == id)
                {
                    return item;
                }
            }
            return null;
        }

        private HashSet<DetailImageNode> FindSetInList(string[] ids)
        {
            var set = new HashSet<DetailImageNode>();
            foreach (var item in warpList)
            {
                if (ids.Contains(item.Model.id))
                {
                    set.Add(item);
                }
            }
            return set;
        }

        public void OnAudioPlaying(string time)
        {
            foreach (var item in warpList)
            {
                if (item.name.Contains("Audio"))
                {
                    DetailAudioNode node = item.GetComponent<DetailAudioNode>();
                    if (node.isPlaying)
                    {
                        node.time = float.Parse(time);
                    }
                }
            }

        }

        public void OnAudioStatusUpdate(string play)
        {
            if (CameraManager.Instance.Mode != CameraManager.CameraMode.normal)
            {
                return;
            }
            if (playId != null)
            {
                DetailImageNode item = FindInList(playId);
                if (item.name.Contains("Audio"))
                {
                    bool value = bool.Parse(play);
                    DetailAudioNode node = item.GetComponent<DetailAudioNode>();
                    node.UpdateStatus(value);
                }
            }
        }

        public void DidStartPlay(string name, bool audio = false)
        {
            NativeAPI.WillPlayMedia();
            playId = null;
            foreach (var item in warpList)
            {
                if (item.name != name)
                {
                    if (item.name.Contains("Video"))
                    {
                        DetailVideoNode node = item.GetComponent<DetailVideoNode>();
                        node.Stop();
                    }
                    if (item.name.Contains("Audio"))
                    {
                        DetailAudioNode node = item.GetComponent<DetailAudioNode>();
                        if (node.isPlaying)
                        {
                            if (audio)
                            {
                                node.StopPlay();
                            }
                            else
                            {
                                node.PlayOrPause();
                            }
                        }
                    }
                }
                else
                {
                    playId = item.model.id;
                }
            }

        }
        public void StopAllItemsPlay()
        {
            playId = null;
            foreach (var item in warpList)
            {
                if (item.name.Contains("Video"))
                {
                    DetailVideoNode node = item.GetComponent<DetailVideoNode>();
                    node.Stop();
                }
                if (item.name.Contains("Audio"))
                {
                    DetailAudioNode node = item.GetComponent<DetailAudioNode>();
                    if (node.isPlaying)
                    {
                        node.StopPlay();

                    }
                }
            }
        }
        private void ChangeLayer(Transform transform, int layer)
        {
            if (transform.childCount > 0)
            {
                for (int i = 0; i < transform.childCount; i++)
                {
                    ChangeLayer(transform.GetChild(i), layer);
                }
            }
            else
            {
                transform.gameObject.layer = layer;
            }
        }

    }
}

