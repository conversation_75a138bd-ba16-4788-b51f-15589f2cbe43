using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;

using UnityEngine;
using UnityEngine.UI;

using UnityImageLoader;

namespace Com.SceneConsole.Arte
{
    public class DetailImageNode : MonoBehaviour
    {
        public ArtefactDetailItem Model
        {
            get => model;
            set
            {
                model = value;
                SetModel(value);
            }
        }

        public bool ContentLoaded
        {
            get => isContentLoaded;
            set
            {
                isContentLoaded = value;
                SetContentLoaded(value);
            }
        }
        /// <summary>
        /// 缩放 0 - 1
        /// </summary>
        public float Scale
        {
            get => scale;
            set
            {
                scale = value;
                SetScale(value);
            }
        }

        [HideInInspector]
        public ArtefactDetailItem model;
        [HideInInspector]
        public Transform canvas;
        private Transform rect;
        [HideInInspector]
        public Transform loading;
        [HideInInspector]
        public Vector3 contentScale;
        [HideInInspector]
        public Transform playBtn;

        private bool isContentLoaded = false;
        private float width;
        private float height;
        private float scale = 1.0f;

        public virtual void Awake()
        {
            loading = transform.Find("Loading");
            canvas = transform.Find("Canvas");
            rect = canvas.Find("Rect");
        }

        public virtual void Update()
        {
            if (loading.gameObject.activeSelf)
            {
                loading.localRotation = Quaternion.Euler(0, 180, loading.eulerAngles.z - 3);
            }
        }

        #region Private
        public virtual void SetModel(ArtefactDetailItem i)
        {
            string snapshot = i.Snapshot();
            if (snapshot.Contains("imageInfo"))
            {
                var query = snapshot.Split('?', '&');
                foreach (var item in query)
                {
                    if (item.Contains("imageInfo"))
                    {
                        var arr = item.Split('=', '_');
                        if (arr.Length == 3)
                        {
                            width = float.Parse(arr[1]);
                            height = float.Parse(arr[2]);
                        }
                    }
                }
                AdjustScaleToFitWidthHeight(width, height);
            }
            if (snapshot.Contains(".gif") || snapshot.Contains(".GIF"))
            {

                var op = DisplayOption.GetDefaultDisplayOption();
                //op.isDiscCache = false;
                op.isMemoryCache = false;
                //GifLoader.GetInstance().GetTexture(snapshot, (data) =>
                //{
                //    Debug.Log("byte load" + data.Length);
                //    if (data != null)
                //    {
                //        if (this != null && gameObject.activeInHierarchy)
                //        {

                //            GetComponent<GifRawImage>().SetGifFromBytes(data, (success) =>
                //            {
                //                ContentLoaded = true;
                //                loading.gameObject.SetActive(false);
                //                if (playBtn != null)
                //                {
                //                    playBtn.gameObject.SetActive(true);
                //                }
                //            });

                //        }
                //        else
                //        {
                //            //Destroy(data);
                //        }
                //    }
                //}, op);
                GifLoader.GetInstance().GetGif(snapshot, (gifTexList, loopCount, width, height) =>
                {
                    ContentLoaded = true;
                    loading.gameObject.SetActive(false);
                    if (playBtn != null)
                    {
                        playBtn.gameObject.SetActive(true);
                    }
                    GetComponent<GifRawImage>().SetGifFromTextureList(gifTexList, loopCount, width, height);
                }, op);
            }
            else
            {

                if (snapshot.Contains("http"))
                {
                    var op = DisplayOption.GetDefaultDisplayOption();
                    //op.isDiscCache = false;
                    op.isMemoryCache = false;
                    ImageLoader.GetInstance().GetTexture(snapshot, (tex) =>
                    {
                        if (tex != null)
                        {
                            if (this != null && gameObject.activeInHierarchy)
                            {
                                ContentLoaded = true;
                                loading.gameObject.SetActive(false);
                                if (playBtn != null)
                                {
                                    playBtn.gameObject.SetActive(true);
                                }
                                canvas.GetComponentInChildren<RawImage>().texture = tex;
                            }
                            else
                            {
                                Destroy(tex);
                            }
                        }
                    }, op);
                }
                else
                {
                    var query = snapshot.Split('?', '&');
                    var fs = new FileStream(query[0], FileMode.Open);
                    byte[] bytes = new byte[fs.Length];
                    fs.Read(bytes, 0, (int)fs.Length);
                    Texture2D tex = new Texture2D(1, 1, TextureFormat.ASTC_8x8, false);
                    tex.LoadImage(bytes);
                    ContentLoaded = true;
                    loading.gameObject.SetActive(false);
                    if (playBtn != null)
                    {
                        playBtn.gameObject.SetActive(true);
                    }
                    canvas.GetComponentInChildren<RawImage>().texture = tex;
                    fs.Close();
                    fs.Dispose();
                }
            }
        }

        public virtual void SetContentLoaded(bool loaded)
        {

        }

        private void SetScale(float s)
        {
            var vector = new Vector3(x: s * 1.5f, y: s * 1.5f, z: s * 1.5f);
            transform.localScale = vector;
        }

        /// <summary>
        /// 格式化时间
        /// </summary>
        /// <param name="seconds">秒</param>
        /// <returns></returns>
        public string FormatTime(float seconds)
        {
            TimeSpan ts = new TimeSpan(0, 0, Convert.ToInt32(seconds));
            string str = "";

            if (ts.Hours > 0)
            {
                str = ts.Hours.ToString("00") + ":" + ts.Minutes.ToString("00") + ":" + ts.Seconds.ToString("00");
            }
            if (ts.Hours == 0 && ts.Minutes > 0)
            {
                str = ts.Minutes.ToString("00") + ":" + ts.Seconds.ToString("00");
            }
            if (ts.Hours == 0 && ts.Minutes == 0)
            {
                str = "00:" + ts.Seconds.ToString("00");
            }

            return str;
        }

        private void AdjustScaleToFitWidthHeight(float width, float height)
        {
            if (width > height)
            {
                float scale = height * 1.0f / width;
                //rect.GetComponent<SpriteRenderer>().size = new Vector2(1.2f, scale + 0.2f);

                //设置scale会拉伸
                rect.transform.localScale = new Vector3(1.2f, scale + 0.2f, 1);
                contentScale = new Vector3(1, scale, 1);
                canvas.Find("RawImage").GetComponent<Transform>().localScale = contentScale;
            }
            else
            {
                float scale = width * 1.0f / height;
                //rect.GetComponent<SpriteRenderer>().size = new Vector2(scale + 0.2f, 1.2f);
                rect.transform.localScale = new Vector3(scale + 0.2f, 1.2f, 1);
                contentScale = new Vector3(scale, 1, 1);
                canvas.Find("RawImage").GetComponent<Transform>().localScale = contentScale;
            }
        }
        public List<Vector3> PointsOfCenterAndEdges()
        {
            var points = new List<Vector3>();
            Vector3 center = new Vector3(0, 0, 0);
            points.Add(center);

            Vector2 size = rect.GetComponent<SpriteRenderer>().size;

            Vector3 edge1 = center + new Vector3(size.x / 2, size.y / 2, 0);
            points.Add(edge1);
            Vector3 edge2 = center + new Vector3(size.x / 2, -size.y / 2, 0);
            points.Add(edge2);
            Vector3 edge3 = center + new Vector3(-size.x / 2, -size.y / 2, 0);
            points.Add(edge3);
            Vector3 edge4 = center + new Vector3(-size.x / 2, size.y / 2, 0);
            points.Add(edge4);

            return points;
        }
        #endregion
    }
}

