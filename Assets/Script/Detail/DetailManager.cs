using System;
using System.Collections;
using System.Collections.Generic;

using UnityEngine;

namespace Com.SceneConsole.Arte
{
    [Serializable]
    public class ArtefactDetailItem
    {
        public string id;
        public string s; //封面
        public string mt; //媒体类型
        public string m; //媒体
        public string t; //时长

        public string Snapshot()
        {
            if (mt == "image")
            {
                return m;
            }
            else
            {
                return s;
            }
        }
    }

    public class DetailManager : MonoBehaviour
    {

        public DetailImageNode imgPrefab;
        public DetailVideoNode videoPrefab;
        public DetailAudioNode audioPrefab;


        private float cursorFrequency;
        private int playIndex;

        public void ShowArtefactDetail(string json)
        {
            HideArtefactDetail();
            
            CameraManager.Instance.Mode = CameraManager.CameraMode.detail;
            transform.position = Camera.main.transform.position;
            transform.rotation = Quaternion.AngleAxis(Camera.main.transform.eulerAngles.y, Vector3.up);
            ArtefactDetailItem[] items = JsonHelper.FromJson<ArtefactDetailItem>(json);
            List<Vector3> points = PointOnSector(items.Length);
            for (int i = 0; i < points.Count; i++)
            {
                Vector3 point = points[i];
                Vector3 cameraPoint = new Vector3(transform.position.x, transform.position.y + point.y, transform.position.z);
                ArtefactDetailItem item = items[i];
                if (item.mt == "image")
                {
                    DetailImageNode node = Instantiate(imgPrefab, point, Quaternion.identity);
                    node.name = "ImageNode" + i;
                    node.Model = item;
                    node.transform.SetParent(transform);
                    //node.transform.localPosition = point;

                    node.transform.LookAt(cameraPoint, Vector3.up);
                    node.transform.position = cameraPoint;

                    //LeanTween.moveLocal(node.gameObject, point, 1.2f);
                    LeanTween.moveLocal(node.gameObject, point, 1.2f).setEase(LeanTweenType.easeInCubic).setOnUpdate((float val) =>
                    {
                        node.transform.LookAt(cameraPoint, Vector3.up);
                    });
                    LeanTween.value(node.gameObject, node.Scale * 0.3f, node.Scale, 1.0f).setEase(LeanTweenType.easeOutCubic).setOnUpdate((float val) =>
                    {
                        node.Scale = val;
                    });

                }
                else if (item.mt == "video")
                {
                    DetailVideoNode node = Instantiate(videoPrefab, point, Quaternion.identity);
                    node.name = "VideoNode" + i;
                    node.Model = item;
                    node.transform.SetParent(transform);
                    //node.transform.localPosition = point;

                    node.transform.LookAt(cameraPoint, Vector3.up);
                    node.transform.position = cameraPoint;

                    //LeanTween.moveLocal(node.gameObject, point, 1.2f);
                    LeanTween.moveLocal(node.gameObject, point, 1.2f).setEase(LeanTweenType.easeInCubic).setOnUpdate((float val) =>
                    {
                        node.transform.LookAt(cameraPoint, Vector3.up);
                    });
                    LeanTween.value(node.gameObject, node.Scale * 0.3f, node.Scale, 1.0f).setEase(LeanTweenType.easeOutCubic).setOnUpdate((float val) =>
                    {
                        node.Scale = val;
                    });
                }
                else if (item.mt == "audio")
                {
                    DetailAudioNode node = Instantiate(audioPrefab, point, Quaternion.identity);
                    node.name = "AudioNode" + i;
                    node.Model = item;
                    node.transform.SetParent(transform);
                    //node.transform.localPosition = point;

                    node.transform.LookAt(cameraPoint, Vector3.up);
                    node.transform.position = cameraPoint;

                    //LeanTween.moveLocal(node.gameObject, point, 1.2f);
                    LeanTween.moveLocal(node.gameObject, point, 1.2f).setEase(LeanTweenType.easeInCubic).setOnUpdate((float val) =>
                    {
                        node.transform.LookAt(cameraPoint, Vector3.up);
                    });
                    LeanTween.value(node.gameObject, node.Scale * 0.3f, node.Scale, 1.0f).setEase(LeanTweenType.easeOutCubic).setOnUpdate((float val) =>
                    {
                        node.Scale = val;
                    });
                }
            }
        }

        public void HideArtefactDetail()
        {
            CameraManager.Instance.Mode = CameraManager.CameraMode.poi;
            int childCount = transform.childCount;
            for (int j = 0; j < childCount; j++)
            {
                Destroy(transform.GetChild(j).gameObject);
            }
            playIndex = -1;
        }

        public void OnAudioPlaying(string time)
        {
            int childCount = transform.childCount;
            for (int j = 0; j < childCount; j++)
            {
                Transform tf = transform.GetChild(j);
                if (tf.name.Contains("Audio"))
                {
                    DetailAudioNode node = tf.GetComponent<DetailAudioNode>();
                    if (node.isPlaying)
                    {
                        node.time = float.Parse(time);
                    }
                }
            }
        }

        public void OnAudioStatusUpdate(string play)
        {
            if (playIndex != -1 && playIndex < transform.childCount)
            {
                Transform tf = transform.GetChild(playIndex);
                if (tf.name.Contains("Audio"))
                {
                    bool value = bool.Parse(play);
                    DetailAudioNode node = tf.GetComponent<DetailAudioNode>();
                    node.UpdateStatus(value);
                }
            }
        }

        public void DidStartPlay(string name, bool audio = false)
        {
            NativeAPI.WillPlayMedia();
            playIndex = -1;
            int childCount = transform.childCount;
            for (int j = 0; j < childCount; j++)
            {
                Transform tf = transform.GetChild(j);
                if (tf.name != name)
                {
                    if (tf.name.Contains("Video"))
                    {
                        DetailVideoNode node = tf.GetComponent<DetailVideoNode>();
                        node.Stop();
                    }
                    if (tf.name.Contains("Audio"))
                    {
                        DetailAudioNode node = tf.GetComponent<DetailAudioNode>();
                        if (node.isPlaying)
                        {
                            if (audio)
                            {
                                node.StopPlay();
                            }
                            else
                            {
                                node.PlayOrPause();
                            }
                        }
                    }
                }
                else
                {
                    playIndex = j;
                }

            }
        }
        public void StopAllDetailPlay()
        {
            playIndex = -1;
            int childCount = transform.childCount;
            for (int j = 0; j < childCount; j++)
            {
                Transform tf = transform.GetChild(j);

                if (tf.name.Contains("Video"))
                {
                    DetailVideoNode node = tf.GetComponent<DetailVideoNode>();
                    node.Stop();
                }
                if (tf.name.Contains("Audio"))
                {
                    DetailAudioNode node = tf.GetComponent<DetailAudioNode>();
                    if (node.isPlaying)
                    {
                        node.StopPlay();

                    }
                }
            }
        }
        public void ChangeRadiusBy(string distance)
        {
            float delta = float.Parse(distance);
            int childCount = transform.childCount;
            for (int j = 0; j < childCount; j++)
            {
                Transform tf = transform.GetChild(j);
                var positionH = new Vector3(0, tf.localPosition.y / 2, 0);
                var dis = (tf.localPosition - positionH).magnitude;

                if (dis > 5 && delta > 0)
                {
                    return;
                }
                if (dis < 1.4 && delta < 0)
                {
                    return;
                }
                var newPosition = tf.localPosition + delta * (tf.localPosition - positionH).normalized;
                tf.localPosition = newPosition;
            }
        }
        public void ResetIdleStatus(string setIdlePositionToCameraForward = "false")
        {
            var arg = bool.Parse(setIdlePositionToCameraForward);
            if (arg)
            {
                transform.position = Camera.main.transform.position;
                transform.rotation = Quaternion.AngleAxis(Camera.main.transform.eulerAngles.y, Vector3.up);
            }
            transform.localScale = new Vector3(1f, 1f, 1f);
            int childCount = transform.childCount;
            var points = PointOnSector(childCount);

            for (int j = 0; j < childCount; j++)
            {
                Transform tf = transform.GetChild(j);
                var point = points[j];
                tf.localPosition = point;
            }
        }
        private List<Vector3> PointOnSector(int count, double radius = 1.5)
        {
            var points = new List<Vector3>();
            //float angle = Mathf.PI / 2;
            float angle = Mathf.PI / 4;
            float height = 0;
            if (count > 4)
            {
                height = -0.6f;
            }
            for (int i = 0; i < count; i++)
            {
                //int x;
                //float h = 0;
                //if (i % 2 == 0)
                //{
                //    x = i / 2;
                //}
                //else
                //{
                //    x = -(i + 1) / 2;
                //}
                //var ang = angle * (double)x;
                //if (i > 7)
                //{
                //    h = height + 3.2f;
                //}
                //else if (i > 3)
                //{
                //    h = height + 1.8f;
                //    ang += Mathf.PI / 4;
                //}
                int x;
                float h = 0;
                int j = i % 5;
                if (j % 2 == 0)
                {
                    x = -j / 2;
                }
                else
                {
                    x = (j + 1) / 2;
                }
                var ang = angle * (double)x;
                if (i > 9)
                {
                    h = height + 3.2f;
                }
                else if (i > 4)
                {
                    h = height + 1.8f;
                }
                Vector3 location = new Vector3((float)(Math.Sin(ang) * radius), h, (float)(Math.Cos(ang) * radius));
                points.Add(location);
            }
            return points;
        }
        private void Update()
        {
            if (cursorFrequency > 0.2f)
            {
                ShowOrHideCursor();
                cursorFrequency = 0;
            }
            else
            {
                cursorFrequency += Time.deltaTime;
            }

        }
        private void ShowOrHideCursor()
        {
            int childCount = transform.childCount;
            for (int j = 0; j < childCount; j++)
            {
                Transform tf = transform.GetChild(j);
                DetailImageNode node = tf.GetComponent<DetailImageNode>();
                List<Vector3> points = node.PointsOfCenterAndEdges();
                for (int i = 0; i < points.Count; i++)
                {
                    Vector3 point = points[i];
                    Vector3 worldPos = tf.TransformPoint(point);
                    if (IsInView(worldPos))
                    {
                        DestroyImmediate(transform.GetChild(0).gameObject.GetComponent<ScreenCursor>());
                        //Debug.Log("InView");
                        return;
                    }
                }
            }
            if (childCount > 0 && transform.GetChild(0).GetComponent<ScreenCursor>() == null)
            {
                transform.GetChild(0).gameObject.AddComponent<ScreenCursor>();
            }

            //Debug.Log("NoNodeInView");
        }
        public bool IsInView(Vector3 worldPos)
        {
            Transform camTransform = Camera.main.transform;
            Vector2 viewPos = Camera.main.WorldToViewportPoint(worldPos);
            Vector3 dir = (worldPos - camTransform.position).normalized;
            float dot = Vector3.Dot(camTransform.forward, dir);     //判断物体是否在相机前面

            if (dot > 0 && viewPos.x >= 0 && viewPos.x <= 1 && viewPos.y >= 0 && viewPos.y <= 1)
                return true;
            else
                return false;
        }
        /*
        private void OnGUI()
        {
            if (GUI.Button(new Rect(10, 200, 200, 40), "ShowArtefactDetail"))
            {
                ShowArtefactDetail("[{\"s\":\"https://oss.sceneconsole.cn/topic/arte/image1642490933296.jpg?imageInfo=1000_562\",\"mt\":\"video\",\"m\":\"https://oss.sceneconsole.cn/fictitious/arte/video/1642490923191537680.mp4\"},{\"s\":\"https://oss.sceneconsole.cn/topic/arte/image1642490933296.jpg?imageInfo=1000_562\",\"mt\":\"video\",\"m\":\"https://oss.sceneconsole.cn/fictitious/arte/video/1642490923191537680.mp4\"},{\"s\":\"https://oss.sceneconsole.cn/topic/arte/image1645065889218.jpeg?imageInfo=2000_829?imageInfo=2000_829\",\"t\":\"7.552\",\"mt\":\"audio\",\"m\":\"https://oss.sceneconsole.cn/fictitious/arte/audio/1645065816522191994.m4a\"},{\"s\":\"https://oss.sceneconsole.cn/topic/arte/image1643253815792.jpeg?imageInfo=645_900\",\"mt\":\"video\",\"m\":\"https://oss.sceneconsole.cn/fictitious/arte/video/1642759161244185970.mp4\",\"t\":\"100\"},{\"s\":\"https://oss.sceneconsole.cn/topic/arte/image1643254110991.jpeg?imageInfo=683_1024\",\"mt\":\"audio\",\"m\":\"https://oss.sceneconsole.cn/fictitious/arte/audio/1643254108990073749.wav\"},{\"mt\":\"image\",\"m\":\"https://oss.sceneconsole.cn/156084950/image/C79A2CE9-48F4-464B-88EE-DB71BC02F607.png?imageInfo=658_3498\"},{\"s\":\"https://oss.sceneconsole.cn/topic/arte/image1643253733767.jpeg?imageInfo=1000_1778\",\"mt\":\"audio\",\"m\":\"https://oss.sceneconsole.cn/fictitious/arte/audio/1643253732369197944.wav\"},{\"s\":\"https://oss.sceneconsole.cn/topic/arte/image1643253702971.jpeg?imageInfo=1000_1778\",\"mt\":\"audio\",\"m\":\"https://oss.sceneconsole.cn/fictitious/arte/audio/1643253701580621293.wav\"},{\"s\":\"https://oss.sceneconsole.cn/topic/arte/image1643014933382.jpeg?imageInfo=645_900\",\"mt\":\"audio\",\"m\":\"https://oss.sceneconsole.cn/fictitious/arte/audio/1643014931831772929.wav\"},{\"s\":\"https://oss.sceneconsole.cn/topic/arte/image1642490933296.jpg?imageInfo=1000_562\",\"mt\":\"video\",\"m\":\"https://oss.sceneconsole.cn/fictitious/arte/video/1642490923191537680.mp4\"},{\"s\":\"https://oss.sceneconsole.cn/topic/arte/image1642490933296.jpg?imageInfo=1000_562\",\"mt\":\"video\",\"m\":\"https://oss.sceneconsole.cn/fictitious/arte/video/1642490923191537680.mp4\"},{\"s\":\"https://oss.sceneconsole.cn/topic/arte/image1642490933296.jpg?imageInfo=1000_562\",\"mt\":\"video\",\"m\":\"https://oss.sceneconsole.cn/fictitious/arte/video/1642490923191537680.mp4\"},{\"s\":\"https://oss.sceneconsole.cn/topic/arte/image1642490933296.jpg?imageInfo=1000_562\",\"mt\":\"video\",\"m\":\"https://oss.sceneconsole.cn/fictitious/arte/video/1642490923191537680.mp4\"},{\"s\":\"https://oss.sceneconsole.cn/topic/arte/image1643253702971.jpeg?imageInfo=1000_1778\",\"mt\":\"audio\",\"m\":\"https://oss.sceneconsole.cn/fictitious/arte/audio/1643253701580621293.wav\"}]");
            }
            if (GUI.Button(new Rect(10, 250, 200, 40), "HideArtefactDetail"))
            {
                HideArtefactDetail();
            }
            if (GUI.Button(new Rect(10, 300, 200, 40), "LayoutArtefactAnimated"))
            {
                ShowArtefactDetail("[{\"s\":\"https://oss.sceneconsole.cn/topic/arte/image1643253815792.jpeg?imageInfo=645_900\",\"mt\":\"video\",\"m\":\"https://arte-encode.oss-cn-beijing.aliyuncs.com/arte/156084611/ef9e3d618b2c6a17e078374dbacf13ff_1.mp4\",\"t\":\"100\"},{\"s\":\"https://oss.sceneconsole.cn/topic/arte/image1643253815792.jpeg?imageInfo=645_900\",\"mt\":\"video\",\"m\":\"https://arte-encode.oss-cn-beijing.aliyuncs.com/arte/156084611/ef9e3d618b2c6a17e078374dbacf13ff_1.mp4\",\"t\":\"100\"},{\"s\":\"https://oss.sceneconsole.cn/topic/arte/image1643253815792.jpeg?imageInfo=645_900\",\"mt\":\"video\",\"m\":\"https://arte-encode.oss-cn-beijing.aliyuncs.com/arte/156084611/ef9e3d618b2c6a17e078374dbacf13ff_1.mp4\",\"t\":\"100\"},{\"s\":\"https://oss.sceneconsole.cn/topic/arte/image1643253815792.jpeg?imageInfo=645_900\",\"mt\":\"video\",\"m\":\"https://arte-encode.oss-cn-beijing.aliyuncs.com/arte/156084611/ef9e3d618b2c6a17e078374dbacf13ff_1.mp4\",\"t\":\"100\"}]");
            }
        }*/
    }

}
