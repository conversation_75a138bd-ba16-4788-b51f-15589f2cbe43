
using TMPro;

using UnityEngine;

namespace Com.SceneConsole.Arte
{
    public class DetailAudioNode : DetailImageNode
    {
        [HideInInspector]
        public bool isPlaying = false;

        [HideInInspector]
        public float time = 0;

        private Transform ani;
        private Transform text;
        private float seconds = 0;

        public override void SetModel(ArtefactDetailItem i)
        {
            playBtn = canvas.Find("PlayBtn");
            playBtn.gameObject.SetActive(false);
            base.SetModel(i);
            BoxCollider box = transform.GetComponent<BoxCollider>();
            box.size = new Vector3(contentScale.x, contentScale.y, 0.1f);
            if (i.t != null)
            {
                seconds = float.Parse(i.t);
            }
            if (contentScale.x >= contentScale.y)
            {
                ani.localScale = Vector3.one;
            }
            else
            {
                ani.localScale = new Vector3(contentScale.x, contentScale.x, 1);
            }
            ani.gameObject.SetActive(false);
            text = canvas.Find("Text");
            text.GetComponent<UnityEngine.UI.Text>().text = FormatTime(seconds);
            text.localPosition = new Vector3(contentScale.x / 2 - 0.04f, -contentScale.y / 2 + 0.03f, 0f);
        }

        public override void SetContentLoaded(bool loaded)
        {
            base.SetContentLoaded(loaded);
            if (loaded)
            {
                ani.gameObject.SetActive(true);
            }
        }

        public override void Awake()
        {
            base.Awake();
            ani = transform.Find("Ani");
        }

        public void PlayOrPause()
        {
            if (!ContentLoaded)
            {
                return;
            }
            if (isPlaying)
            {
                NativeAPI.PausePlayAudio(model.m);
                playBtn.gameObject.SetActive(true);
                transform.GetComponentInChildren<GifImage>().enabled = false;
            }
            else
            {
                playBtn.gameObject.SetActive(false);
                transform.GetComponentInChildren<GifImage>().enabled = true;
                var detailManager = transform.GetComponentInParent<DetailManager>();
                var fixedAdManager = transform.GetComponentInParent<FixedAdManager>();
                var locationAdManager = transform.GetComponentInParent<LocationAdManager>();
                if (detailManager != null)
                {
                    detailManager.DidStartPlay(name, true);
                }
                if (fixedAdManager != null)
                {
                    fixedAdManager.DidStartPlay(name, true);
                }
                if (locationAdManager != null)
                {
                    locationAdManager.DidStartPlay(name, true);
                }

                NativeAPI.StartPlayAudio(model.m, model.s);
            }
            isPlaying = !isPlaying;
        }

        public void StopPlay()
        {
            NativeAPI.StopPlayAudio(model.m);
            playBtn.gameObject.SetActive(true);
            transform.GetComponentInChildren<GifImage>().enabled = false;
            isPlaying = false;
            time = 0;
            text.GetComponent<UnityEngine.UI.Text>().text = FormatTime(seconds);
        }

        public void UpdateStatus(bool play)
        {
            if (!ContentLoaded)
            {
                return;
            }
            if (play != isPlaying)
            {
                isPlaying = play;
                if (play)
                {
                    playBtn.gameObject.SetActive(false);
                    transform.GetComponentInChildren<GifImage>().enabled = true;
                    var detailManager = transform.GetComponentInParent<DetailManager>();
                    var fixedAdManager = transform.GetComponentInParent<FixedAdManager>();
                    var locationAdManager = transform.GetComponentInParent<LocationAdManager>();
                    if (detailManager != null)
                    {
                        detailManager.DidStartPlay(name, true);
                    }
                    if (fixedAdManager != null)
                    {
                        fixedAdManager.DidStartPlay(name, true);
                    }
                    if (locationAdManager != null)
                    {
                        locationAdManager.DidStartPlay(name, true);
                    }
                }
                else
                {
                    playBtn.gameObject.SetActive(true);
                    transform.GetComponentInChildren<GifImage>().enabled = false;
                }
            }
        }

        private void OnDestroy()
        {
            if (isPlaying)
            {
                NativeAPI.StopPlayAudio(model.m);
            }
        }

        public override void Update()
        {
            base.Update();
            if (seconds > 0)
            {
                if (isPlaying)
                {
                    text.GetComponent<UnityEngine.UI.Text>().text = FormatTime(Mathf.Max(0, seconds - time));
                }
            }
        }
    }
}
