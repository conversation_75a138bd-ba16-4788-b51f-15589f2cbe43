#if UNITY_ANDROID
using RenderHeads.Media.AVProVideo;
#endif
using UnityEngine;
using UnityEngine.UI;
using UnityEngine.Video;

namespace Com.SceneConsole.Arte
{
    public class DetailVideoNode : DetailImageNode
    {
        [HideInInspector]
        public bool isPlaying = false;

        private Transform videoPlayer;
        private float seconds = 0;
        private Transform text;

        public override void SetModel(ArtefactDetailItem i)
        {
            playBtn = canvas.Find("PlayBtn");
            playBtn.gameObject.SetActive(false);
            base.SetModel(i);
            BoxCollider box = transform.GetComponent<BoxCollider>();
            box.size = new Vector3(contentScale.x, contentScale.y, 0.1f);
            videoPlayer.localScale = contentScale;
            if (i.t != null)
            {
                seconds = float.Parse(i.t);
            }
            text = canvas.Find("Text");
            text.GetComponent<UnityEngine.UI.Text>().text = FormatTime(seconds);
            text.localPosition = new Vector3(contentScale.x / 2 - 0.03f, -contentScale.y / 2 + 0.03f, 0f);
#if UNITY_ANDROID
            Transform avplayer = canvas.Find("AVPro Video");
            avplayer.localScale = contentScale;
#endif
        }

        public override void Awake()
        {
            base.Awake();
#if UNITY_ANDROID
            videoPlayer = transform.Find("MediaPlayer");
#else
            videoPlayer = transform.Find("Player");
#endif
        }

        public void PlayOrPause()
        {
            if (!ContentLoaded)
            {
                return;
            }
            if (videoPlayer.gameObject.activeSelf)
            {
#if UNITY_ANDROID
                MediaPlayer player = videoPlayer.GetComponent<MediaPlayer>();
#else
                VideoPlayer player = videoPlayer.GetComponent<VideoPlayer>();
#endif
                if (isPlaying)
                {
                    //Debug.Log("isPlaying, will stop");
                    isPlaying = false;
                    player.Pause();
                    playBtn.gameObject.SetActive(true);
                    loading.gameObject.SetActive(false);
                }
                else
                {
                    //Debug.Log("isStop, will play");
                    player.Play();
                    playBtn.gameObject.SetActive(false);
                    var detailManager = transform.GetComponentInParent<DetailManager>();
                    var fixedAdManager = transform.GetComponentInParent<FixedAdManager>();
                    var locationAdManager = transform.GetComponentInParent<LocationAdManager>();
                    if (detailManager != null)
                    {
                        detailManager.DidStartPlay(name);
                    }
                    if (fixedAdManager != null)
                    {
                        fixedAdManager.DidStartPlay(name);
                    }
                    if (locationAdManager != null)
                    {
                        locationAdManager.DidStartPlay(name);
                    }
                    isPlaying = true;
                }
            }
            else
            {
                //Debug.Log("noactive");
                videoPlayer.gameObject.SetActive(true);
#if UNITY_ANDROID
                MediaPlayer player = videoPlayer.GetComponent<MediaPlayer>();
                player.OpenMedia(MediaPathType.AbsolutePathOrURL, model.m, false);
                player.Events.AddListener(OnEventsHandler);
                player.Play();
#else
                VideoPlayer player = videoPlayer.GetComponent<VideoPlayer>();
                player.prepareCompleted += PlayerLoadingCompleted;
                player.errorReceived += delegate (VideoPlayer videoPlayer, string message)
                {
                    Debug.LogWarning("[VideoPlayer] Play Movie Error: " + message);
                };
                player.enabled = true;
                player.url = model.m;
                player.Play();
#endif
                isPlaying = true;
                playBtn.gameObject.SetActive(false);
                //canvas.GetComponentInChildren<RawImage>().enabled = false;
                loading.gameObject.SetActive(true);
                var detailManager = transform.GetComponentInParent<DetailManager>();
                var fixedAdManager = transform.GetComponentInParent<FixedAdManager>();
                var locationAdManager = transform.GetComponentInParent<LocationAdManager>();
                if (detailManager != null)
                {
                    detailManager.DidStartPlay(name);
                }
                if (fixedAdManager != null)
                {
                    fixedAdManager.DidStartPlay(name);
                }
                if (locationAdManager != null)
                {
                    locationAdManager.DidStartPlay(name);
                }
                //Debug.Log("toplay");
                AudioOutput output = transform.GetComponentInChildren<AudioOutput>();
                if (output != null)
                {
                    output.isRecording = true;
                }
            }
        }

        public void Stop()
        {
            if (isPlaying)
            {
                PlayOrPause();
            }
#if UNITY_ANDROID
            MediaPlayer player = videoPlayer.GetComponent<MediaPlayer>();
            player.Stop();
            player.CloseMedia();
#else
            VideoPlayer player = videoPlayer.GetComponent<VideoPlayer>();
            player.Stop();
#endif

            AudioOutput output = transform.GetComponentInChildren<AudioOutput>();
            if (output != null)
            {
                output.isRecording = false;
            }
            videoPlayer.gameObject.SetActive(false);
            canvas.GetComponentInChildren<RawImage>().enabled = true;
            text.GetComponent<UnityEngine.UI.Text>().text = FormatTime(seconds);
        }

        public override void Update()
        {
            base.Update();
            if (seconds > 0)
            {
#if UNITY_ANDROID
                MediaPlayer player = videoPlayer.GetComponent<MediaPlayer>();
                if (player != null && player.Control != null && player.Control.IsPlaying())
                {
                    text.GetComponent<UnityEngine.UI.Text>().text = FormatTime(Mathf.Max(0, seconds - (float)player.Control.GetCurrentTime()));
                }
#else
                VideoPlayer player = videoPlayer.GetComponent<VideoPlayer>();
                if (player != null && player.isPlaying)
                {
                    text.GetComponent<UnityEngine.UI.Text>().text = FormatTime(Mathf.Max(0, seconds - (float)player.time));
                }
#endif
            }
        }

        private void PlayerLoadingCompleted(VideoPlayer source)
        {
            canvas.GetComponentInChildren<RawImage>().enabled = false;
            loading.gameObject.SetActive(false);
        }

        private void OnApplicationPause(bool pause)
        {
            if (pause && isPlaying)
            {
                PlayOrPause();
            }
        }
#if UNITY_ANDROID
        public void OnEventsHandler(MediaPlayer player, MediaPlayerEvent.EventType type, ErrorCode code)
        {
            if (type == MediaPlayerEvent.EventType.FirstFrameReady)
            {
                canvas.GetComponentInChildren<RawImage>().enabled = false;
                loading.gameObject.SetActive(false);
            }
        }
#endif
    }
}

