using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Com.SceneConsole.Arte
{
    public class GameObjectModelTypeHolder : MonoBehaviour
    {
        public enum GameObjectModelType
        {
            None,//未指定 0，无需处理
            BillboardPlane,//引导牌 1
            FoodMapPlane,//烟火地图（旧版-平面）
            ImagePlane,//双面图片
            Particle,//粒子效果
            ThreeDModelRoot,//3d模型的根节点
            Container,//容器，内部有子元素
            SpiritualFortress,//精神堡垒（新版-烟火地图）
            Other1,//其他待定
            Other2,//其他待定
            Other3//其他待定
        }
        public GameObjectModelType gameObjectModelType = GameObjectModelType.None;
        // Start is called before the first frame update
        void Start()
        {

        }

        // Update is called once per frame
        void Update()
        {

        }
    }
}