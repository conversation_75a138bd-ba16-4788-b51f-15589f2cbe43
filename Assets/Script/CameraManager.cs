using System.Collections;
using System.Collections.Generic;

using UnityEngine;

namespace Com.SceneConsole.Arte
{
    [DisallowMultipleComponent]
    public class CameraManager : Singleton<CameraManager>
    {
        public enum CameraMode
        {
            noneAR, //无ar模式
            normal, //正常模式
            paint, //绘画模式
            answer, //答案之书
            detail, //ar详情
            poi, //poi标记
        }

        public GameObject arte_camera;
        public GameObject paint_camera;
        public GameObject answer_camera;
        public GameObject detail_camera;
        public GameObject poi_camera;

        private CameraMode m_mode = CameraMode.poi;
        private bool m_noneAr = false;
        private bool artefactShow = true;

        [HideInInspector]
        public CameraMode Mode
        {
            get => m_mode; set
            {
                m_mode = value;
#if UNITY_ANDROID
                // 修改安卓卡顿
                StartCoroutine(SetModeAsync(value));
#else
                SetMode(value);
#endif
            }
        }
        
        public void ShowOrHideArtefact(string value)
        {
            artefactShow = bool.Parse(value);
            if (m_mode == CameraMode.normal)
            {
                arte_camera.SetActive(artefactShow);
            }
        }

        private void SetMode(CameraMode mode)
        {
            switch (mode)
            {
                case CameraMode.normal:
                    arte_camera.SetActive(artefactShow);
                    paint_camera.SetActive(false);
                    answer_camera.SetActive(false);
                    detail_camera.SetActive(false);
                    poi_camera.SetActive(false);
                    break;
                case CameraMode.paint:
                    arte_camera.SetActive(false);
                    paint_camera.SetActive(true);
                    answer_camera.SetActive(false);
                    detail_camera.SetActive(false);
                    poi_camera.SetActive(false);
                    for (int i = 0; i < paint_camera.transform.childCount; i++)
                    {
                        var paintCamera = paint_camera.transform.GetChild(i).gameObject.GetComponent<Camera>();
                        if (paintCamera != null)
                        {
                            paintCamera.projectionMatrix = Camera.main.projectionMatrix;
                        }
                        
                    }
                    break;
                case CameraMode.answer:
                    arte_camera.SetActive(false);
                    paint_camera.SetActive(false);
                    answer_camera.SetActive(true);
                    detail_camera.SetActive(false);
                    poi_camera.SetActive(false);
                    break;
                case CameraMode.noneAR:
                    arte_camera.SetActive(false);
                    paint_camera.SetActive(false);
                    answer_camera.SetActive(false);
                    detail_camera.SetActive(false);
                    poi_camera.SetActive(false);
                    break;
                case CameraMode.detail:
                    arte_camera.SetActive(false);
                    paint_camera.SetActive(false);
                    answer_camera.SetActive(false);
                    detail_camera.SetActive(true);
                    poi_camera.SetActive(false);
                    break;
                case CameraMode.poi:
                    arte_camera.SetActive(false);
                    paint_camera.SetActive(false);
                    answer_camera.SetActive(false);
                    detail_camera.SetActive(false);
                    poi_camera.SetActive(true);
                    break;
                default:
                    break;
            }
        }

        private IEnumerator SetModeAsync(CameraMode mode)
        {
            if (m_noneAr)
            {
                yield break;
            }
            switch (mode)
            {
                case CameraMode.normal:
                    arte_camera.SetActive(artefactShow);
                    paint_camera.SetActive(false);
                    answer_camera.SetActive(false);
                    detail_camera.SetActive(false);
                    poi_camera.SetActive(false);
                    break;
                case CameraMode.paint:
                    arte_camera.SetActive(false);
                    answer_camera.SetActive(false);
                    detail_camera.SetActive(false);
                    poi_camera.SetActive(false);
                    yield return new WaitForEndOfFrame();
                    paint_camera.SetActive(true);
                    for (int i = 0; i < paint_camera.transform.childCount; i++)
                    {
                        yield return new WaitForEndOfFrame();
                        var paintCamera = paint_camera.transform.GetChild(i).gameObject.GetComponent<Camera>();
                        if (paintCamera != null) {
                            paintCamera.projectionMatrix = Camera.main.projectionMatrix;
                        }
                        
                    }
                    break;
                case CameraMode.answer:
                    arte_camera.SetActive(false);
                    paint_camera.SetActive(false);
                    answer_camera.SetActive(true);
                    detail_camera.SetActive(false);
                    poi_camera.SetActive(false);
                    break;
                case CameraMode.noneAR:
                    m_noneAr = true;
                    arte_camera.SetActive(false);
                    paint_camera.SetActive(false);
                    answer_camera.SetActive(false);
                    detail_camera.SetActive(true);
                    poi_camera.SetActive(false);
                    break;
                case CameraMode.detail:
                    arte_camera.SetActive(false);
                    paint_camera.SetActive(false);
                    answer_camera.SetActive(false);
                    detail_camera.SetActive(true);
                    poi_camera.SetActive(false);
                    break;
                case CameraMode.poi:
                    arte_camera.SetActive(false);
                    paint_camera.SetActive(false);
                    answer_camera.SetActive(false);
                    detail_camera.SetActive(false);
                    poi_camera.SetActive(true);
                    break;
                default:
                    break;
            }
        }

    }
}

