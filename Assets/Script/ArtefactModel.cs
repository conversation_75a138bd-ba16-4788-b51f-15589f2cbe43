using System;

namespace Com.SceneConsole.Arte
{
    [Serializable]
    public class ArtefactUnity
    {
        public string id;
        public string pos;
        public float height;
        public int shape;
        public int media;
        public string color;
        public string[] prePhotos;
        public string content;
        public string imagePath; //本地的发送的直接设置地址
        public string videoPath; //本地的发送的直接设置地址
        public bool initial;
        public int bizType;

        public int layoutMode;
    }

    [Serializable]
    public class ArtefactLayout
    {
        public enum LayoutType
        {
            remote = 0,
            publish, //本地发布
            recommend //推荐
        }

        public string id;
        public float x;
        public float y;
        public float height;
        public float scale;
        public float distance;
        public string color;
        public LayoutType type; //是否是本地位置
    }

    [Serializable]
    public class Layoutor
    {
        public int mode; //mode = 0 默认位置 1 球型预览
        public bool animated;
        public ArtefactLayout[] layouts;
    }

    [Serializable]
    public class PoiUnity
    {
        public string id;
        public string name;
        public string type;
        public string typecode;
        public string address;
        public int distance;
        public string hexColor;
        public int facility;//0其他；1 医院；2 派出所；3 加油站；4 卫生间；5 便利店
    }
}
