using System;
using UnityEngine;
using UnityEngine.UI;

namespace Com.SceneConsole.Arte
{
    [ExecuteInEditMode]
    public sealed class BillboardPlaneNode: MonoBehaviour
    {
        
        public enum FocusAnimation
        {
            None,
            Rotate,
            Scale
        }

        public enum FocusInfo
        {
            None,
            Distance,
            Name,
            Both
        }
        public static readonly string Tag = "BillboardPlaneNode";
        [HideInInspector]
        public Transform container;
        [HideInInspector]
        public Transform canvas;
        [HideInInspector]
        public Transform image;
        [HideInInspector]
        public Transform textPanelBg;
        [HideInInspector]
        public Transform nameText;
        [HideInInspector]
        public Transform distanceText;
        [HideInInspector]
        public string id;

        [Header("Focus Reaction Settings")]
        public FocusAnimation focusAnimation = FocusAnimation.Rotate;
        [SerializeField]
        public float rotateDuration = 1f;
        [SerializeField]
        public float rotateDegree = 360f;
        [SerializeField]
        public float scaleDuration = 0.2f;
        [SerializeField]
        public float scaleFactor = 1.2f;
        public FocusInfo focusInfo = FocusInfo.Distance;
        [SerializeField]
        public bool isNavTarget = true;
        
        [Header("Display Settings")]
        public float heightToFit = 60f;
        [SerializeField]
        public Sprite billboardImage;
        
        [SerializeField]
        [Header("Auto Face Camera Settings")]
        public bool autoFaceToCamera = false;
        [Range(0f, 15f)]
        public float rotationSpeed = 5f; // 旋转速度，0表示瞬间旋转
        public bool smoothRotation = false; // 是否使用平滑旋转

        private float targetRotationY = 0f; // 目标旋转角度
        private float lastCameraCheckTime = 0f;
        private Vector3 lastCameraPosition;
        private const float CameraCheckInterval = 0.5f; // 相机位置检查间隔
        private const float DistanceCheckInterval = 0.5f; // 位置检查距离间距
        private float originalRotationY;
        private Vector3 originalScale;
       

        public void Awake()
        {
            container = transform.Find("Container");
            canvas = container.Find("Canvas");
            image = canvas.Find("Image");
            textPanelBg = canvas.Find("TextPanelBg");
            nameText = textPanelBg.Find("NameText");
            distanceText = textPanelBg.Find("DistanceText");
            image.gameObject.SetActive(true);
            textPanelBg.gameObject.SetActive(false);
        }
        // Start is called before the first frame update
        void Start()
        {
            id = transform.position.ToString();
            // 获取组件引用，避免重复调用
            var imageComponent = image.GetComponent<Image>();
            var canvasRectTransform = canvas.GetComponent<RectTransform>();

            if (!imageComponent || !canvasRectTransform || !billboardImage)
            {
                Debug.LogError("Missing required components!");
                return;
            }

            // 设置图片
            imageComponent.sprite = billboardImage;
        }

        private void Update()
        {
            
            
#if UNITY_EDITOR
            return;
#endif
            if (autoFaceToCamera && Camera.main != null)
            {
                // 性能优化：只在相机位置发生显著变化或达到检查间隔时才重新计算
                var currentTime = Time.time;
                var cameraP = Camera.main.transform.position;

                bool shouldRecalculate = false;

                if (currentTime - lastCameraCheckTime >= CameraCheckInterval)
                {
                    if (Vector3.Distance(cameraP, lastCameraPosition) > DistanceCheckInterval)
                    {
                        shouldRecalculate = true;
                        lastCameraPosition = cameraP;
                    }
                    lastCameraCheckTime = currentTime;
                }

                if (shouldRecalculate)
                {
                    targetRotationY = CalculateBestFacingAngle(cameraP, transform.position, container.localEulerAngles.y);
                }
                // Debug.Log("autoFaceToCamera"+targetRotationY);
                // 应用旋转（平滑或瞬间）
                if (smoothRotation && rotationSpeed > 0f)
                {
                    // 平滑旋转
                    var currentY = container.localEulerAngles.y;
                    var newY = Mathf.LerpAngle(currentY, targetRotationY, rotationSpeed * Time.deltaTime);
                    container.localEulerAngles = new Vector3(container.localEulerAngles.x, newY, container.localEulerAngles.z);
                }
                else
                {
                    // 瞬间旋转
                    container.localEulerAngles = new Vector3(container.localEulerAngles.x, targetRotationY, container.localEulerAngles.z);
                }
            }
        }

        /// <summary>
        /// 计算在当前朝向基础上，旋转0、90、180、270度中与相机夹角最小的角度
        /// </summary>
        /// <param name="cameraPosition">相机位置</param>
        /// <param name="objectPosition">物体位置</param>
        /// <param name="currentRotationY">当前Y轴旋转角度</param>
        /// <returns>最佳的旋转角度</returns>
        private float CalculateBestFacingAngle(Vector3 cameraPosition, Vector3 objectPosition, float currentRotationY)
        {
            // 计算从物体到相机的方向向量（只考虑XZ平面）
            var directionToCamera = new Vector3(cameraPosition.x - objectPosition.x, 0, cameraPosition.z - objectPosition.z).normalized;

            // 基于当前朝向，计算四个可能的旋转角度（在当前角度基础上+0, +90, +180, +270）
            float[] rotationOffsets = { 0f, 90f, 180f, 270f };

            // 找到与相机角度最接近的旋转角度
            float bestAngle = currentRotationY;
            float minAngleDifference = float.MaxValue;

            foreach (float offset in rotationOffsets)
            {
                // 计算在当前角度基础上加上偏移后的角度
                float candidateAngle = currentRotationY + offset;

                // 标准化角度到0-360范围
                while (candidateAngle >= 360f) candidateAngle -= 360f;
                while (candidateAngle < 0f) candidateAngle += 360f;

                // 创建一个临时的旋转，表示候选的本地旋转
                Quaternion candidateRotation = Quaternion.Euler(0, candidateAngle, 0);

                // 计算候选旋转下的本地前方向
                Vector3 candidateLocalForward = candidateRotation * Vector3.back;

                // 将本地方向转换为世界坐标方向
                Vector3 candidateWorldForward = transform.TransformDirection(candidateLocalForward);

                // 计算候选世界前方向与相机方向的夹角
                float angleDiff = Vector3.Angle(candidateWorldForward, directionToCamera);

                if (angleDiff < minAngleDifference)
                {
                    minAngleDifference = angleDiff;
                    bestAngle = candidateAngle;
                }
            }

            return bestAngle;
        }

        void OnValidate() // 当Inspector中的值改变时自动调用
        {
            if (billboardImage != null)
            {
                UpdateImageSpriteAndRect();
            }
        }

        void UpdateImageSpriteAndRect()
        {
            id = transform.position.ToString();
            if (!image || !canvas)
            {
                return;
            }
            // 获取组件引用，避免重复调用
            var imageComponent = image.GetComponent<Image>();
            var canvasRectTransform = canvas.GetComponent<RectTransform>();

            if (!imageComponent || !canvasRectTransform || !billboardImage)
            {
                Debug.LogError("Missing required components!");
                return;
            }

            gameObject.name = Tag + "(" + billboardImage.name + ")";
            // 设置图片
            imageComponent.sprite = billboardImage;

            // 计算宽高比和适配尺寸
            var factor = billboardImage.rect.width / billboardImage.rect.height;
            var fitWidth = factor * heightToFit;

            // 设置image尺寸
            imageComponent.rectTransform.sizeDelta = new Vector2(fitWidth, heightToFit);

            // 设置image居中：使用anchor居中而不是position偏移
            imageComponent.rectTransform.anchorMin = new Vector2(0.5f, 0.5f);
            imageComponent.rectTransform.anchorMax = new Vector2(0.5f, 0.5f);
            imageComponent.rectTransform.anchoredPosition = Vector2.zero;

            // 设置canvas尺寸
            canvasRectTransform.sizeDelta = new Vector2(fitWidth, heightToFit);
            
            GetComponent<BoxCollider>().size = new Vector3(fitWidth, heightToFit, 0.1f);

            if (imageComponent != null && billboardImage != null)
            {
                imageComponent.sprite = billboardImage;

#if UNITY_EDITOR
                // 标记为已修改，确保保存
                UnityEditor.EditorUtility.SetDirty(imageComponent);
                UnityEditor.EditorUtility.SetDirty(this);
#endif
            }
        }
        public void SetDisplayDistance(int d)
        {
            distanceText.GetComponent<Text>().text = FormatDistance(d);
        }
        public void SetDisplayName(string n)
        {
            nameText.GetComponent<Text>().text = n;
        }

        public void StartPreview()
        {
            originalRotationY = transform.localEulerAngles.y;
            originalScale = transform.localScale;
            switch (focusAnimation)
            {
                case FocusAnimation.Rotate:
                    // 旋转1周（360度），持续1秒
                    LeanTween.rotateAroundLocal(gameObject, Vector3.up, rotateDegree, rotateDuration)
                        .setEase(LeanTweenType.easeInOutQuad)
                        .setOnComplete(SetFocusInfo);
                    break;
                case FocusAnimation.Scale:
                    // ，持续0.2秒
                    LeanTween.scale(gameObject, originalScale * scaleFactor, scaleDuration)
                        .setEase(LeanTweenType.easeInOutQuad)
                        .setOnComplete(SetFocusInfo);
                    break;
                case FocusAnimation.None:
                    SetFocusInfo();
                    break;
                default:
                    SetFocusInfo();
                    break;
            }
        }

        private void SetFocusInfo()
        {
            switch (focusInfo)
            {
                case FocusInfo.Distance:
                    if (Camera.main)
                    {
                        var distance = transform.position - Camera.main.transform.position;
                        var disStr = FormatDistance((int)distance.magnitude);
                        distanceText.GetComponent<Text>().text = disStr;
                    }
                    image.gameObject.SetActive(false);  // 隐藏image
                    textPanelBg.gameObject.SetActive(true);  // 显示textPanelBg
                    nameText.gameObject.SetActive(false);  // 不显示nameText
                    distanceText.gameObject.SetActive(true);  // 显示distanceText
                    break;
                case FocusInfo.Name:
                    image.gameObject.SetActive(false);  // 隐藏image
                    textPanelBg.gameObject.SetActive(true);  // 显示textPanelBg
                    nameText.gameObject.SetActive(true);  // 显示nameText
                    distanceText.gameObject.SetActive(false);  // 不显示distanceText
                    break;
                case FocusInfo.Both:
                    if (Camera.main)
                    {
                        var distance = transform.position - Camera.main.transform.position;
                        var disStr = FormatDistance((int)distance.magnitude);
                        distanceText.GetComponent<Text>().text = disStr;
                    }
                    image.gameObject.SetActive(false);  // 隐藏image
                    textPanelBg.gameObject.SetActive(true);  // 显示textPanelBg
                    nameText.gameObject.SetActive(true);  // 显示nameText
                    distanceText.gameObject.SetActive(true);  // 显示distanceText
                    break;
                case FocusInfo.None:
                default:
                    break;
            }
        }
        public void StopPreview()
        {
            transform.localEulerAngles = new Vector3(0, originalRotationY, 0);
            transform.localScale = originalScale;
            image.gameObject.SetActive(true);
            textPanelBg.gameObject.SetActive(false);
        }
        private string FormatDistance(int dis)
        {
            //if (dis == null)
            //{
            //    return "";
            //}
            if (dis >= 100000)
            {
                var num = dis / 1000;
                return num.ToString() + "km";
            }
            else if (dis >= 1000)
            {
                var num = dis / 1000.0f;
                return num.ToString("#.#") + "km";
            }
            else if (dis < 20)
            {
                return "已在附近";
            }
            else
            {
                return dis.ToString() + "m";
            }

        }

    }
}