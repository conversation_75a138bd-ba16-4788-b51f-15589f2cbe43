using System.Collections;
using System.Collections.Generic;

using ARLocation.Utils;

using UnityEngine;

namespace Com.SceneConsole.Arte
{

    [System.Serializable]
    public class CLHeading
    {
        public double trueHeading;
        public double magneticHeading;
        public double headingAccuracy;
        public long timestamp;
    }

    /// <summary>
    /// This component should be placed on the "ArtefactsRoot" GameObject (which should be a child of the
    /// "AR Session Origin") for correctly aligning the coordinate system to the north/east geographical lines.
    /// </summary>
    [DisallowMultipleComponent]
    public class OrientationManager : MonoBehaviour
    {

        public Camera mainCamera;

        [Header("Update Settings")]

        [Tooltip("The maximum number of orientation updates. The updates will be paused after this amount. Zero means there is no limit and " +
        "the updates won't be paused automatically.")]
        public uint MaxNumberOfUpdates = 4;


        /// <summary>
        /// Only update after measuring the heading N times, and take the average.
        /// </summary>
        [Tooltip("Only update after measuring the heading N times, and take the average."), Range(1, 500)]
        [Header("Averaging")]
        public int AverageCount = 150;

        /// <summary>
        /// If set to true, use raw heading values until measuring the first average.
        /// </summary>
        [Tooltip("If set to true, use raw heading values until measuring the first average.")]
        public bool UseRawUntilFirstAverage = true;

        /// <summary>
        /// The smoothing factor. Zero means disabled. Values around 100 seem to give good results.
        /// </summary>
        [Tooltip("The smoothing factor. Zero means disabled.")]
        [Header("Smoothing")]
        [Range(0.0f, 1.0f)]
        public float MovementSmoothingFactor = 0.015f;

        /// <summary>
        /// A custom offset to the device-calculated true north direction.
        /// </summary>
        [Tooltip("A custom offset to the device-calculated true north direction. When set to a value other than zero, the device's true north will be ignored, and replaced by the " +
                 "magnetic heading added to this offset.")]
        [Header("Calibration")]
        public float TrueNorthOffset;

        /// <summary>
        /// 旋转是否停止
        /// </summary>
        [HideInInspector]
        public bool Stable;

        private int updateCounter;
        private List<float> values = new List<float>();
        private bool isFirstAverage = true;
        private float targetAngle;
        private bool isChangingOrientation;
        private Transform mainCameraTransform;

        /// <summary>
        /// Restarts the orientation tracking.
        /// </summary>
        public void Restart()
        {
            Stable = false;
            isFirstAverage = true;
            updateCounter = 0;
            values = new List<float>();
            targetAngle = 0;
            isChangingOrientation = false;
            targetAngle = mainCameraTransform.rotation.eulerAngles.y;
        }

        //ar 暂停后持续更新 防止回来后更新闪一下
        public void ContinueUpdate()
        {
            MaxNumberOfUpdates = 0;
        }

        public void ResetMaxUpdates()
        {
            Stable = false;
            MaxNumberOfUpdates = 3;
            isFirstAverage = true;
            updateCounter = 0;
            values = new List<float>();
        }

        // Use this for initialization
        void Start()
        {
            mainCameraTransform = mainCamera.transform;

            targetAngle = mainCameraTransform.rotation.eulerAngles.y;
        }

        public void OnCompassUpdatedHandler(string json)
        {
            CLHeading newHeading = JsonUtility.FromJson<CLHeading>(json);

            if (MaxNumberOfUpdates > 0 && updateCounter >= MaxNumberOfUpdates)
            {
                return;
            }

            var trueHeading = (Mathf.Abs(TrueNorthOffset) > 0.000001f) ? newHeading.magneticHeading + TrueNorthOffset : newHeading.trueHeading;


            float currentCameraHeading = mainCameraTransform.rotation.eulerAngles.y;
            float value = Misc.GetNormalizedDegrees(currentCameraHeading - ((float)trueHeading));

            if (Mathf.Abs(value) < 0.0000001f)
            {
                Stable = true;
                return;
            }

            // If averaging is not enabled
            if (AverageCount <= 1)
            {
                if (updateCounter == 0)
                {
                    transform.localRotation = Quaternion.AngleAxis(value, Vector3.up);
                    TrySetOrientation(value, true);
                }
                else
                {
                    TrySetOrientation(value);
                }

                return;
            }

            values.Add(value);

            if (updateCounter == 0 && values.Count == 1)
            {
                TrySetOrientation(value, true);
                return;
            }


            if (isFirstAverage && UseRawUntilFirstAverage)
            {
                TrySetOrientation(value, true);
                return;
            }

            if (values.Count >= AverageCount)
            {
                if (isFirstAverage)
                {
                    isFirstAverage = false;
                }

                var average = Misc.FloatListAverage(values);
                values.Clear();

                TrySetOrientation(average);
            }
        }

        private void TrySetOrientation(float angle, bool isFirstUpdate = false)
        {
            if (isFirstUpdate)
            {
                targetAngle = angle;

                //print("场景旋转" + targetAngle);

                transform.localRotation = Quaternion.AngleAxis(angle, Vector3.up);

                Stable = true;

                updateCounter++;
                return;
            }

            if (MaxNumberOfUpdates > 0 && updateCounter >= MaxNumberOfUpdates)
            {
                return;
            }

            targetAngle = angle;
            isChangingOrientation = true;
            updateCounter++;
        }

        private void Update()
        {
            if (Mathf.Abs(transform.rotation.eulerAngles.y - targetAngle) <= 0.001f)
            {
                if (isChangingOrientation)
                {
                    isChangingOrientation = false;
                }
                return;
            }

            //print("场景旋转" + targetAngle);

            var t = 1.0f - Mathf.Pow(MovementSmoothingFactor, Time.deltaTime);
            var value = Mathf.LerpAngle(transform.rotation.eulerAngles.y, targetAngle, t);

            transform.localRotation = Quaternion.AngleAxis(value, Vector3.up);
        }

        //private void OnGUI()
        //{
        //    if (GUI.Button(new Rect(10, 200, 200, 100), "OnCompassUpdatedHandler"))
        //    {
        //        OnCompassUpdatedHandler("{\"headingAccuracy\": 16.748929977416992, 	\"trueHeading\": 259.28671264648438, 	\"magneticHeading\": 266.4931640625, 	\"timestamp\": 1586415612.444145 }");
        //    }
        //}

    }
}
