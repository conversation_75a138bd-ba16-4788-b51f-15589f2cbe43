using System.Collections;
using System.Collections.Generic;
using System.Linq;

using UnityEngine;
using UnityEngine.Video;

namespace Com.SceneConsole.Arte
{
    public class ArtefactManager : MonoBehaviour
    {

        /// <summary>
        /// 预制件
        /// </summary>
        public ArtefactWarp prefab;

        /// <summary>
        /// 摄像机
        /// </summary>
        public Camera mainCamera;

        /// <summary>
        /// ArtefactWarp的根节点
        /// </summary>
        public GameObject rootNode;

        /// <summary>
        /// 初始化位置 距离屏幕
        /// </summary>
        public float initialDistance = 2f;

        /// <summary>
        /// 球型预览根节点
        /// </summary>
        public GameObject sphereRoot;

        /// <summary>
        /// 广告根节点
        /// </summary>
        public GameObject adRoot;

        [HideInInspector]
        public bool layouting = false;

        private readonly HashSet<ArtefactWarp> warpList = new HashSet<ArtefactWarp>();

        private ArtefactWarp debugArte;

        #region Interface
        /// <summary>
        /// 发布Arte
        /// </summary>
        /// <param name="json"></param>
        public void CreateArtefact(string json)
        {
#if DEBUG
            Debug.Log(json);
#endif
            ArtefactUnity arti = JsonUtility.FromJson<ArtefactUnity>(json);
            arti.initial = true;
            //arti.layoutMode = ArtefactWarp.LayoutMode.unity.GetHashCode();
            var warp = CreateArtefactUnity(arti);
        }

        /// <summary>
        /// 添加Artes
        /// </summary>
        /// <param name="json"></param>
        public void AddArtefacts(string json)
        {
#if DEBUG
            Debug.Log(json);
#endif
            ArtefactUnity[] list = JsonHelper.FromJson<ArtefactUnity>(json);

            for (int i = 0; i < list.Length; i++)
            {
                var model = list[i];
                if (model.layoutMode == 0)
                {
                    CreateArtefactUnity(model);
                }
                else
                {
                    CreateARArtefactUnity(model);
                }
            }
        }

        /// <summary>
        /// 移除Artes
        /// </summary>
        /// <param name="json"></param>
        public void RemoveArtefacts(string json)
        {
#if DEBUG
            Debug.Log(json);
#endif
            string[] list = JsonHelper.FromJson<string>(json);

            var artis = FindSetInList(list);
            foreach (var item in artis)
            {
                DestoryArtefact(item);
            }
        }

        /// <summary>
        /// 带动画的移除Arte
        /// </summary>
        /// <param name="id"></param>
        public void DissolveArtefact(string id)
        {
            ArtefactWarp match = FindInList(id);
            if (match != null)
            {
                DissolveArtefact(match);
            }
        }

        /// <summary>
        /// 不带动画的移除Arte
        /// </summary>
        /// <param name="id"></param>
        public void DestoryArtefact(string id)
        {
            ArtefactWarp match = FindInList(id);
            if (match != null)
            {
                DestoryArtefact(match);
            }
        }

        /// <summary>
        /// 更新Artefact的位置 大小
        /// </summary>
        /// <param name="json"></param>
        public void LayoutArtefact(string json)
        {
#if DEBUG
            Debug.Log(json);
#endif
            Layoutor layoutor = JsonUtility.FromJson<Layoutor>(json);

            if (layoutor.mode == 0)
            {
                NormalLayoutDidUpdate(layoutor.layouts, layoutor.animated);
            }
            else
            {
                SphereLayoutDidUpdate(layoutor.layouts, layoutor.animated);
            }
        }

        /// <summary>
        /// 切换摄像机滤镜
        /// </summary>
        /// <param name="filter"></param>
        public void SwitchCameraFilter(string id)
        {
            FilterManager.Instance.SwitchCameraFilter(id);
        }

        /// <summary>
        /// 添加debug
        /// </summary>
        public void BeginDebugDistance(string id)
        {
            if (debugArte != null)
            {
                DestroyImmediate(debugArte.gameObject.GetComponent<ScreenCursor>());
                debugArte = null;
            }
            foreach (var item in warpList)
            {
                if (item.id == id)
                {
                    //item.gameObject.AddComponent<DebugDistance>();
                    item.gameObject.AddComponent<ScreenCursor>();
                    if (item.artefact.ArtefactIsOpen())
                    {
                        item.artefact.Close();
                    }
                    item.artefact.gameObject.SetActive(true);
                    item.artefact.Alpha = 1;
                    debugArte = item;
                }
                else
                {
                    item.artefact.gameObject.SetActive(false);
                }
            }
            //锁定时，出现游标才提示跟着游标去寻找
            if (debugArte != null)
            {
                LeanTween.delayedCall(0.6f, () =>
                 {
                     var cursor = debugArte.gameObject.GetComponent<ScreenCursor>();
                     if (cursor.image.isActiveAndEnabled)
                     {
                         NativeAPI.OnArtefactTracked();
                     }
                 });
            }
        }

        public void EndDebugDistance()
        {
            if (debugArte != null)
            {
                //Destroy(debugArte.gameObject.GetComponent<DebugDistance>());
                DestroyImmediate(debugArte.gameObject.GetComponent<ScreenCursor>());
                debugArte = null;
            }
            foreach (var item in warpList)
            {
                item.artefact.gameObject.SetActive(true);
            }
        }

        /// <summary>
        /// 重置本地arte的位置
        /// </summary>
        public void ResetLocalArtesPosition()
        {
            var tf = Camera.main.transform;
            foreach (var item in warpList)
            {
                if (item.initialPosition != Vector3.zero)
                {
                    var trans = tf.InverseTransformPoint(item.transform.position);
                    item.initialPosition = trans;
                }
            }
        }

        public void ShowOrHideAd()
        {
            if (adRoot.activeSelf)
            {
                adRoot.SetActive(false);
            }
            else
            {
                adRoot.transform.position = mainCamera.transform.position;
                adRoot.transform.rotation = Quaternion.AngleAxis(mainCamera.transform.eulerAngles.y, Vector3.up);
                adRoot.SetActive(true);
            }
        }

        #endregion

        #region Open & Close
        public void OpenArtefact(ArtefactWarp arte)
        {
            if (!arte.artefact.ArtefactIsOpen())
            {
                NativeAPI.DidClickArte(arte.id);
                arte.artefact.OpenDetail(() =>
                {
                    if (arte == null)
                    {
                        NativeAPI.OpenArteDetail("");
                    }
                    else
                    {
                        if (arte.bizType == 1)
                        {
                            NativeAPI.OpenArteDetail(arte.id);
                        }
                        else if (arte.bizType == 2)
                        {
                            NativeAPI.OpenThematicDetail(arte.id);
                        }
                        else
                        {
                            NativeAPI.OpenArteDetail(arte.id);
                        }
                    }
                });
            }
        }

        public void CloseArtefact(string id)
        {
            ArtefactWarp warp = FindInList(id);
            if (warp != null)
            {
                if (warp.artefact.ArtefactIsOpen())
                {
                    warp.artefact.CloseDetail();
                }
                else
                {
                    warp.artefact.CloseAnimation();
                }
            }
        }

        public void CloseArtefactAnimatedNo(string id)
        {
            ArtefactWarp warp = FindInList(id);
            if (warp != null)
            {
                warp.artefact.Close();
            }
        }

        public void DissolveArtefact(ArtefactWarp arte)
        {
            if (debugArte != null)
            {
                if (debugArte.id == arte.id)
                {
                    EndDebugDistance();
                }
            }
            warpList.Remove(arte);
            if (arte.artefact.ArtefactIsOpen())
            {
                arte.artefact.CloseDissolve(() =>
                {
                    Destroy(arte.gameObject);
                });
            }
            else
            {
                arte.artefact.Dissolve(() =>
                {
                    Destroy(arte.gameObject);
                });
            }
        }

        public void DestoryArtefact(ArtefactWarp arte)
        {
            if (debugArte != null)
            {
                if (debugArte.id == arte.id)
                {
                    EndDebugDistance();
                }
            }
            warpList.Remove(arte);
            arte.artefact.AnimatedDestory();
        }

        public void VanishArtefact(string id)
        {
            ArtefactWarp item = FindInList(id);
            if (item != null)
            {
                if (debugArte != null)
                {
                    if (debugArte.id == item.id)
                    {
                        EndDebugDistance();
                    }
                }
                warpList.Remove(item);
                item.artefact.Vanish(() =>
                {
                    Destroy(item.gameObject);
                });
            }
            else
            {
                //如果找不到说明是刚发布直接溜走
                ArtefactWarp warp = Instantiate(prefab, Vector3.zero, Quaternion.identity) as ArtefactWarp;
                warp.artefact.Alpha = 0;
                warp.artefact.mainCamera = mainCamera;
                warp.transform.SetParent(rootNode.transform);
                warp.artefact.Vanish(() =>
                {
                    Destroy(warp.gameObject);
                });
            }
        }
        #endregion

        private float captureTime;

        private void Update()
        {
            //检测跟摄像机的位置防止接触
            //foreach (ArtefactWarp warp in warpList)
            //{
            //    warp.Move(mainCamera.transform.position);
            //}

            sphereRoot.transform.position = mainCamera.transform.position;
            //sphereRoot.transform.localRotation = rootNode.transform.localRotation;

            //rootNode.transform.position = mainCamera.transform.position;

            //获取本地发送的列表并更新
            float time = Time.time;
            if (time < captureTime)
            {
                return;
            }
            else
            {
                captureTime = time + 2;
            }

            var list = new List<ArtefactLayout>();
            foreach (var item in warpList)
            {
                if (item.initial)
                {
                    var trans = item.transform.localPosition - rootNode.transform.InverseTransformPoint(mainCamera.transform.position);
                    var layout = new ArtefactLayout
                    {
                        id = item.id,
                        x = trans.x,
                        y = trans.z,
                        distance = Vector3.Distance(transform.position, item.transform.position),
                        type = ArtefactLayout.LayoutType.publish,
                        color = item.hexColor,
                        height = 0,
                        scale = 1,
                    };
                    list.Add(layout);
                }

            }
            if (list.Count > 0)
            {
                var json = JsonHelper.ToJson<ArtefactLayout>(list.ToArray());
                NativeAPI.LocalPublishArtiLayout(json);
            }
            else
            {
                NativeAPI.LocalPublishArtiLayout("");
            }
        }

        /// <summary>
        /// 创建ArtefactWarp
        /// </summary>
        /// <param name="arti"></param>
        /// <returns></returns>
        private ArtefactWarp CreateArtefactUnity(ArtefactUnity arti)
        {
            Vector3 targetPos = mainCamera.transform.position;
            targetPos.y += 2;

            ArtefactWarp warp = Instantiate(prefab, targetPos, Quaternion.identity);
            warp.id = arti.id;

            //判断是否添加成功
            if (warpList.Add(warp))
            {
                warp.InitialWithUnity(arti);
                warp.transform.SetParent(rootNode.transform);

                if (arti.initial)
                {
                    warp.published = false;
                    //添加到发布列表
                    transform.GetComponent<UnityInitialize>().PushPublisher(warp);
                }
                else
                {
                    warp.published = true;
                }
                if (debugArte != null) //如果在锁定模式，设置禁用
                {
                    warp.artefact.gameObject.SetActive(false);
                }
                return warp;
            }
            else
            {
                Destroy(warp.gameObject);
            }

            return null;
        }

        private ArtefactWarp FindInList(string id)
        {
            foreach (var item in warpList)
            {
                if (item.id == id)
                {
                    return item;
                }
            }
            return null;
        }

        private HashSet<ArtefactWarp> FindSetInList(string[] ids)
        {
            var set = new HashSet<ArtefactWarp>();
            foreach (var item in warpList)
            {
                if (ids.Contains(item.id))
                {
                    set.Add(item);
                }
            }
            return set;
        }

        /// <summary>
        /// 创建AR Artefact
        /// </summary>
        /// <param name="arti"></param>
        /// <returns></returns>
        private ArtefactWarp CreateARArtefactUnity(ArtefactUnity arti)
        {
            ArtefactWarp warp = CreateArtefactUnity(arti);
            //var strings = arti.pos.Split(',');

            //var location = new Location()
            //{
            //    Latitude = double.Parse(strings[0]),
            //    Longitude = double.Parse(strings[1]),
            //    Altitude = double.Parse(arti.height),
            //    AltitudeMode = AltitudeMode.DeviceRelative,
            //};
            //var setting = new Hotspot.HotspotSettingsData()
            //{
            //    PositionMode = Hotspot.PositionModes.HotspotCenter,
            //    ActivationRadius = 30000, //视野范围暂定300m
            //    AlignToCamera = false,
            //    Prefab = prefab.gameObject,
            //};

            //Hotspot.AddHotspotComponent(warp.gameObject, location, setting);

            //var loc = new Location()
            //{
            //    Latitude = double.Parse(strings[0]),
            //    Longitude = double.Parse(strings[1]),
            //    Altitude = arti.height,
            //    AltitudeMode = AltitudeMode.DeviceRelative
            //};

            //var opts = new PlaceAtLocation.PlaceAtOptions()
            //{
            //    HideObjectUntilItIsPlaced = true,
            //    MaxNumberOfLocationUpdates = 2,
            //    MovementSmoothing = 0.1f,
            //    UseMovingAverage = false
            //};

            //PlaceAtLocation.AddPlaceAtComponent(warp.gameObject, loc, opts, true);

            return warp;
        }

        private static Transform initialCameraTF;

        /// <summary>
        /// 更新位置大小
        /// </summary>
        private void NormalLayoutDidUpdate(ArtefactLayout[] layouts, bool animated)
        {
            if (initialCameraTF == null)
            {
                initialCameraTF = mainCamera.transform;
            }
            foreach (var layout in layouts)
            {
                ArtefactWarp match = FindInList(layout.id);
                if (match != null)
                {
                    if (layout.type != ArtefactLayout.LayoutType.recommend && !match.published)
                    {
                        continue;
                    }

                    match.transform.parent = rootNode.transform;
                    var reletiveVector = rootNode.transform.right * layout.x + rootNode.transform.forward * layout.y;
                    Vector3 vector = mainCamera.transform.position + reletiveVector;
                    vector.y = match.initialHeight + layout.height + mainCamera.transform.position.y - 1.5f;

                    if (animated)
                    {
                        switch (layout.type)
                        {
                            case ArtefactLayout.LayoutType.remote:
                                LeanTween.move(match.gameObject, vector, 1.2f);
                                if (!match.artefact.ArtefactIsPreviewing())
                                {
                                    LeanTween.value(match.gameObject, match.artefact.Scale, layout.scale * 0.3f, 1.0f).setEase(LeanTweenType.easeOutCubic).setOnUpdate((float val) =>
                                    {
                                        match.artefact.Scale = val;
                                    });
                                }
                                break;
                            case ArtefactLayout.LayoutType.publish:
                                LeanTween.move(match.gameObject, match.initialPosition, 1.2f);
                                if (!match.artefact.ArtefactIsPreviewing())
                                {
                                    LeanTween.value(match.gameObject, match.artefact.Scale, 0.3f, 1.0f).setEase(LeanTweenType.easeOutCubic).setOnUpdate((float val) =>
                                    {
                                        match.artefact.Scale = val;
                                    });
                                }
                                break;
                            case ArtefactLayout.LayoutType.recommend:
                                if (match.initialPosition == Vector3.zero)
                                {
                                    var offset = mainCamera.transform.right * layout.x + mainCamera.transform.forward * layout.y;
                                    Vector3 position = mainCamera.transform.position + offset;
                                    position.y = layout.height + mainCamera.transform.position.y - 1.5f;
                                    match.initialPosition = position;
                                }
                                match.artefact.Recommond();
                                LeanTween.move(match.gameObject, match.initialPosition, 1.2f);
                                if (!match.artefact.ArtefactIsPreviewing())
                                {
                                    LeanTween.value(match.gameObject, match.artefact.Scale, 0.3f, 1.0f).setEase(LeanTweenType.easeOutCubic).setOnUpdate((float val) =>
                                    {
                                        match.artefact.Scale = val;
                                    });
                                }
                                break;
                            default:
                                break;
                        }
                        if (!match.artefact.ArtefactIsAnimating())
                        {
                            LeanTween.value(match.gameObject, match.artefact.Alpha, 1.0f, 1.5f).setEase(LeanTweenType.easeOutCubic).setOnUpdate((float val) =>
                            {
                                match.artefact.Alpha = val;
                            });
                        }
                    }
                    else
                    {
                        if (!match.artefact.ArtefactIsAnimating())
                        {
                            match.artefact.Alpha = 1;
                        }
                        switch (layout.type)
                        {
                            case ArtefactLayout.LayoutType.remote:
                                match.transform.position = vector;
                                if (!match.artefact.ArtefactIsPreviewing())
                                {
                                    match.artefact.Scale = layout.scale * 0.3f;
                                }
                                break;
                            case ArtefactLayout.LayoutType.publish:
                                match.transform.position = match.initialPosition;
                                if (!match.artefact.ArtefactIsPreviewing())
                                {
                                    match.artefact.Scale = 0.3f;
                                }
                                break;
                            case ArtefactLayout.LayoutType.recommend:
                                if (match.initialPosition == Vector3.zero)
                                {
                                    var offset = mainCamera.transform.right * layout.x + mainCamera.transform.forward * layout.y;
                                    Vector3 position = mainCamera.transform.position + offset;
                                    position.y = layout.height + mainCamera.transform.position.y - 1.5f;
                                    match.initialPosition = position;
                                }
                                match.artefact.Recommond();
                                match.transform.position = match.initialPosition;
                                if (!match.artefact.ArtefactIsPreviewing())
                                {
                                    match.artefact.Scale = 0.3f;
                                }
                                break;
                            default:
                                break;
                        }
                    }
                }
            }
            layouting = true;
            LeanTween.delayedCall(2.0f, () =>
            {
                layouting = false;
            });
        }

        /// <summary>
        /// 球型预览
        /// </summary>
        /// <param name="layout"></param>
        private void SphereLayoutDidUpdate(ArtefactLayout[] layouts, bool animated)
        {
            foreach (var layout in layouts)
            {
                ArtefactWarp match = FindInList(layout.id);
                if (match != null)
                {
                    match.transform.parent = sphereRoot.transform;

                    if (!match.published)
                    {
                        continue;
                    }

                    var vector = new Vector3()
                    {
                        x = layout.x,
                        y = layout.height,
                        z = layout.y,
                    };

                    if (animated)
                    {
                        switch (layout.type)
                        {
                            case ArtefactLayout.LayoutType.remote:
                            case ArtefactLayout.LayoutType.publish:
                                LeanTween.moveLocal(match.gameObject, vector, 1.2f).setEase(LeanTweenType.easeOutCubic);
                                if (!match.artefact.ArtefactIsPreviewing())
                                {
                                    LeanTween.value(match.gameObject, match.artefact.Scale, layout.scale * 0.3f, 1.2f).setEase(LeanTweenType.easeOutCubic).setOnUpdate((float val) =>
                                    {
                                        match.artefact.Scale = val;
                                    });
                                }
                                if (!match.artefact.ArtefactIsAnimating())
                                {
                                    LeanTween.value(match.gameObject, match.artefact.Alpha, 1.0f, 1.5f).setEase(LeanTweenType.easeOutCubic).setOnUpdate((float val) =>
                                    {
                                        match.artefact.Alpha = val;
                                    });
                                }
                                break;
                            case ArtefactLayout.LayoutType.recommend:
                                match.artefact.Recommond();
                                LeanTween.moveLocal(match.gameObject, vector, 1.2f).setEase(LeanTweenType.easeOutCubic);
                                if (!match.artefact.ArtefactIsPreviewing())
                                {
                                    LeanTween.value(match.gameObject, match.artefact.Scale, layout.scale * 0.3f, 1.2f).setEase(LeanTweenType.easeOutCubic).setOnUpdate((float val) =>
                                    {
                                        match.artefact.Scale = val;
                                    });
                                }
                                if (!match.artefact.ArtefactIsAnimating())
                                {
                                    LeanTween.value(match.gameObject, match.artefact.Alpha, 1.0f, 1.5f).setEase(LeanTweenType.easeOutCubic).setOnUpdate((float val) =>
                                    {
                                        match.artefact.Alpha = val;
                                    });
                                }
                                break;
                            default:
                                break;
                        }
                    }
                    else
                    {
                        switch (layout.type)
                        {
                            case ArtefactLayout.LayoutType.remote:
                            case ArtefactLayout.LayoutType.publish:
                                match.transform.localPosition = vector;
                                if (!match.artefact.ArtefactIsPreviewing())
                                {
                                    match.artefact.Scale = layout.scale * 0.3f;
                                }
                                if (!match.artefact.ArtefactIsAnimating())
                                {
                                    match.artefact.Alpha = 1;
                                }
                                break;
                            case ArtefactLayout.LayoutType.recommend:
                                match.artefact.Recommond();
                                match.transform.localPosition = vector;
                                if (!match.artefact.ArtefactIsPreviewing())
                                {
                                    match.artefact.Scale = layout.scale * 0.3f;
                                }
                                if (!match.artefact.ArtefactIsAnimating())
                                {
                                    match.artefact.Alpha = 1;
                                }
                                break;
                            default:
                                break;
                        }
                    }

                }
            }
            layouting = true;
            LeanTween.delayedCall(2.0f, () =>
            {
                layouting = false;
            });
        }

        /// <summary>
        /// 每当接收到新的（过滤的）位置数据时发出
        /// </summary>
        /// <param name="location"></param>
        //public void LocationDidUpdated(Location location)
        //{
        //    NativeAPI.OnLocationUpdated(System.Math.Round(location.Latitude, 7) + "," + System.Math.Round(location.Longitude, 7));
        //}

        /*
        private void OnGUI()
        {
            if (GUI.Button(new Rect(10, 200, 200, 40), "CreateArtefact"))
            {
                //CreateArtefact(ArteGeometry.dodeca, Artefact.Type.text, Color.red);
                AddArtefacts("[{\"id\": \"Z7guz15507079\", 	\"style\": \"yellow\", 	\"prePhotos\": [\"https:\\/\\/oss.sceneconsole.cn\\/156084624\\/image\\/1606459794.jpg\"], 	\"shape\": \"3\", 	\"pos\": \"32.506579,99.597943\", 	\"isOpend\": 2, 	\"color\": \"#FCD783\", 	\"userId\": 156084624, 	\"layoutMode\": 0, 	\"height\": 0.059872591495513938, 	\"media\": 3, 	\"kind\": 1 }, { 	\"id\": \"BWIVA15507484\", 	\"style\": \"dark\", 	\"prePhotos\": [\"https:\\/\\/oss.sceneconsole.cn\\/156082116\\/image\\/1607505394.png\"], 	\"shape\": \"3\", 	\"pos\": \"39.975671,116.484061\", 	\"isOpend\": 2, 	\"color\": \"#12113E\", 	\"userId\": 156082116, 	\"layoutMode\": 0, 	\"height\": 0.23754726648330687, 	\"media\": 3, 	\"kind\": 2 }, { 	\"id\": \"KnuRq15507493\", 	\"style\": \"blue\", 	\"prePhotos\": [\"https:\\/\\/oss.sceneconsole.cn\\/156082323\\/image\\/1607567891.jpg\"], 	\"shape\": \"1\", 	\"pos\": \"39.975138,116.499324\", 	\"isOpend\": 2, 	\"color\": \"#394EFF\", 	\"userId\": 156082323, 	\"layoutMode\": 0, 	\"height\": -0.096635091304779042, 	\"media\": 4, 	\"kind\": 2 }, { 	\"id\": \"uOQac15507134\", 	\"style\": \"red\", 	\"prePhotos\": [\"https:\\/\\/oss.sceneconsole.cn\\/156084611\\/image\\/1606475605.jpg\"], 	\"shape\": \"2\", 	\"pos\": \"39.975553,116.483886\", 	\"isOpend\": 2, 	\"color\": \"#B72542\", 	\"userId\": 156084611, 	\"layoutMode\": 0, 	\"height\": 0.2418196678161621, 	\"media\": 3, 	\"kind\": 1 }, { 	\"id\": \"3WAJz15507226\", 	\"style\": \"dark\", 	\"prePhotos\": [\"https:\\/\\/oss.sceneconsole.cn\\/snaptshot\\/export_1606896221_200.jpg\", \"https:\\/\\/oss.sceneconsole.cn\\/snaptshot\\/export_1606896221_400.jpg\", \"https:\\/\\/oss.sceneconsole.cn\\/snaptshot\\/export_1606896221_600.jpg\", \"https:\\/\\/oss.sceneconsole.cn\\/snaptshot\\/export_1606896221_800.jpg\", \"https:\\/\\/oss.sceneconsole.cn\\/snaptshot\\/export_1606896221_1000.jpg\", \"https:\\/\\/oss.sceneconsole.cn\\/snaptshot\\/export_1606896221_1200.jpg\", \"https:\\/\\/oss.sceneconsole.cn\\/snaptshot\\/export_1606896221_1400.jpg\", \"https:\\/\\/oss.sceneconsole.cn\\/snaptshot\\/export_1606896221_1600.jpg\", \"https:\\/\\/oss.sceneconsole.cn\\/snaptshot\\/export_1606896221_1800.jpg\", \"https:\\/\\/oss.sceneconsole.cn\\/snaptshot\\/export_1606896221_2000.jpg\"], 	\"shape\": \"2\", 	\"pos\": \"39.975845,116.484051\", 	\"isOpend\": 2, 	\"color\": \"#12113E\", 	\"userId\": 156084464, 	\"layoutMode\": 0, 	\"height\": 0.19808228015899659, 	\"media\": 2, 	\"kind\": 2 }, { 	\"id\": \"Mfxgo15507494\", 	\"style\": \"orange\", 	\"prePhotos\": [\"https:\\/\\/oss.sceneconsole.cn\\/156082186\\/image\\/1607569209.png\"], 	\"shape\": \"4\", 	\"pos\": \"39.975891,116.484081\", 	\"isOpend\": 2, 	\"color\": \"#D7651D\", 	\"userId\": 156082186, 	\"layoutMode\": 0, 	\"height\": -0.1191530406475067, 	\"media\": 3, 	\"kind\": 2 }, { 	\"id\": \"GSXK815507491\", 	\"style\": \"red\", 	\"prePhotos\": [\"https:\\/\\/oss.sceneconsole.cn\\/156082323\\/image\\/1607509077.png\"], 	\"shape\": \"2\", 	\"pos\": \"39.975856,116.484051\", 	\"isOpend\": 2, 	\"color\": \"#B72542\", 	\"userId\": 156082323, 	\"layoutMode\": 0, 	\"height\": -0.17870401740074157, 	\"media\": 3, 	\"kind\": 2 }, { 	\"id\": \"aEDTJ15507486\", 	\"style\": \"orange\", 	\"prePhotos\": [\"https:\\/\\/oss.sceneconsole.cn\\/156082116\\/image\\/1607505798.png\"], 	\"shape\": \"0\", 	\"pos\": \"39.975707,116.484049\", 	\"isOpend\": 2, 	\"color\": \"#D7651D\", 	\"userId\": 156082116, 	\"layoutMode\": 0, 	\"height\": -0.23228175491094588, 	\"media\": 3, 	\"kind\": 2 }, { 	\"id\": \"LDvNb15507483\", 	\"style\": \"orange\", 	\"prePhotos\": [\"https:\\/\\/oss.sceneconsole.cn\\/156082116\\/image\\/1607505116.png\"], 	\"shape\": \"0\", 	\"pos\": \"39.975889,116.484124\", 	\"isOpend\": 2, 	\"color\": \"#D7651D\", 	\"userId\": 156082116, 	\"layoutMode\": 0, 	\"height\": 0.15764890909194945, 	\"media\": 3, 	\"kind\": 2 }, { 	\"id\": \"CUdEs15507478\", 	\"style\": \"red\", 	\"prePhotos\": [\"https:\\/\\/oss.sceneconsole.cn\\/156084661\\/image\\/1607501023.png\"], 	\"shape\": \"1\", 	\"pos\": \"39.975838,116.483992\", 	\"isOpend\": 2, 	\"color\": \"#B72542\", 	\"userId\": 156084661, 	\"layoutMode\": 0, 	\"height\": 0.29596234560012818, 	\"media\": 3, 	\"kind\": 2 }, { 	\"id\": \"GrdK415507225\", 	\"style\": \"pink\", 	\"prePhotos\": [\"https:\\/\\/oss.sceneconsole.cn\\/156084464\\/image\\/1606896211.png\"], 	\"shape\": \"3\", 	\"pos\": \"39.975845,116.484051\", 	\"isOpend\": 2, 	\"color\": \"#C745BC\", 	\"userId\": 156084464, 	\"layoutMode\": 0, 	\"height\": 0.15089328289031984, 	\"media\": 3, 	\"kind\": 2 }, { 	\"id\": \"1Mn0Y15507224\", 	\"style\": \"azora\", 	\"prePhotos\": [\"https:\\/\\/oss.sceneconsole.cn\\/156084464\\/image\\/1606896205.png\"], 	\"shape\": \"4\", 	\"pos\": \"39.975845,116.484051\", 	\"isOpend\": 2, 	\"color\": \"#388292\", 	\"userId\": 156084464, 	\"layoutMode\": 0, 	\"height\": 0.13508208990097048, 	\"media\": 3, 	\"kind\": 2 }, { 	\"id\": \"lEKXZ15507223\", 	\"style\": \"dark\", 	\"prePhotos\": [\"https:\\/\\/oss.sceneconsole.cn\\/snaptshot\\/export_1606896059_200.jpg\", \"https:\\/\\/oss.sceneconsole.cn\\/snaptshot\\/export_1606896059_400.jpg\", \"https:\\/\\/oss.sceneconsole.cn\\/snaptshot\\/export_1606896059_600.jpg\", \"https:\\/\\/oss.sceneconsole.cn\\/snaptshot\\/export_1606896059_800.jpg\", \"https:\\/\\/oss.sceneconsole.cn\\/snaptshot\\/export_1606896059_1000.jpg\", \"https:\\/\\/oss.sceneconsole.cn\\/snaptshot\\/export_1606896059_1200.jpg\", \"https:\\/\\/oss.sceneconsole.cn\\/snaptshot\\/export_1606896059_1400.jpg\", \"https:\\/\\/oss.sceneconsole.cn\\/snaptshot\\/export_1606896059_1600.jpg\", \"https:\\/\\/oss.sceneconsole.cn\\/snaptshot\\/export_1606896059_1800.jpg\", \"https:\\/\\/oss.sceneconsole.cn\\/snaptshot\\/export_1606896059_2000.jpg\"], 	\"shape\": \"2\", 	\"pos\": \"39.975882,116.484157\", 	\"isOpend\": 2, 	\"color\": \"#12113E\", 	\"userId\": 156084464, 	\"layoutMode\": 0, 	\"height\": -0.26235562562942505, 	\"media\": 2, 	\"kind\": 1 }]");
            }
            if (GUI.Button(new Rect(10, 250, 200, 40), "MoveArtefact"))
            {
                LayoutArtefact("{\"mode\": 0, 	\"animated\": true, 	\"layouts\": [{ 		\"x\": 0.80000000000000004, 		\"height\": 1.3, 		\"distance\": 1, 		\"y\": 0.80000000000000004, 		\"id\": \"eK1wN15506146\", 		\"type\": 0, 		\"scale\": 0.84999999999999998 	}, { 		\"x\": 12.285633404988124, 		\"height\": 1.3, 		\"distance\": 13.062270757643088, 		\"y\": 4.4368781615772797, 		\"id\": \"LDvNb15507483\", 		\"type\": 0, 		\"scale\": 2.8736995666814797 	}, { 		\"x\": -5.196152422706632, 		\"height\": 0, 		\"distance\": 0, 		\"y\": 3.0000000000000009, 		\"id\": \"uOQac15507134\", 		\"type\": 2, 		\"scale\": 1 	}, { 		\"x\": 6.0496514788285376, 		\"height\": 1.3, 		\"distance\": 6.0988059316920555, 		\"y\": 0.7727505779549585, 		\"id\": \"GSXK815507491\", 		\"type\": 0, 		\"scale\": 1.6222823778300866 	}, { 		\"x\": 0.10000000000000001, 		\"height\": 1.3, 		\"distance\": 1, 		\"y\": 0.10000000000000001, 		\"id\": \"uUdz215506141\", 		\"type\": 0, 		\"scale\": 0.84999999999999998 	}, { 		\"x\": 0.80000000000000004, 		\"height\": 1.3, 		\"distance\": 1, 		\"y\": 0.80000000000000004, 		\"id\": \"CUdEs15507478\", 		\"type\": 0, 		\"scale\": 0.84999999999999998 	}, { 		\"x\": 15.104638933401711, 		\"height\": 1.3, 		\"distance\": 15.541661396279634, 		\"y\": 3.6596389768139339, 		\"id\": \"LvkV015507217\", 		\"type\": 0, 		\"scale\": 3.4191655071815195 	}, { 		\"x\": 8.6123837780485015, 		\"height\": 1.3, 		\"distance\": 9.7917926322838156, 		\"y\": 4.6589464994048546, 		\"id\": \"Mfxgo15507494\", 		\"type\": 0, 		\"scale\": 2.6046168401874947 	}, { 		\"x\": 6.0496514788285376, 		\"height\": 1.3, 		\"distance\": 6.0662632755032151, 		\"y\": -0.44862528325248213, 		\"id\": \"1Mn0Y15507224\", 		\"type\": 0, 		\"scale\": 1.6136260312838553 	}, { 		\"x\": 12.541906635395703, 		\"height\": 1.3, 		\"distance\": 13.034269159725492, 		\"y\": 3.5486048079001464, 		\"id\": \"8LtHi15507006\", 		\"type\": 0, 		\"scale\": 2.8675392151396086 	}, { 		\"x\": 5.196152422706632, 		\"height\": 0, 		\"distance\": 0, 		\"y\": 3.0000000000000009, 		\"id\": \"3WAJz15507226\", 		\"type\": 2, 		\"scale\": 1 	}, { 		\"x\": 4.1703144611000988, 		\"height\": 1.3, 		\"distance\": 5.198977134436034, 		\"y\": 3.1044681306671071, 		\"id\": \"iJ4wB15506293\", 		\"type\": 0, 		\"scale\": 1.3829279177599849 	}, { 		\"x\": -2.9999999999999996, 		\"height\": 0, 		\"distance\": 0, 		\"y\": 5.196152422706632, 		\"id\": \"BWIVA15507484\", 		\"type\": 2, 		\"scale\": 1 	}, { 		\"x\": 5.8788026593661211, 		\"height\": 1.3, 		\"distance\": 16.831475149730704, 		\"y\": -15.771340632013436, 		\"id\": \"aEDTJ15507486\", 		\"type\": 0, 		\"scale\": 3.702924532940755 	}, { 		\"x\": 15.104638933401711, 		\"height\": 1.3, 		\"distance\": 15.541661396279634, 		\"y\": 3.6596389768139339, 		\"id\": \"XwrWQ15507219\", 		\"type\": 0, 		\"scale\": 3.4191655071815195 	}, { 		\"x\": 0, 		\"height\": 0, 		\"distance\": 0, 		\"y\": 6, 		\"id\": \"Z7guz15507079\", 		\"type\": 2, 		\"scale\": 1 	}, { 		\"x\": 2.9999999999999996, 		\"height\": 0, 		\"distance\": 0, 		\"y\": 5.196152422706632, 		\"id\": \"KnuRq15507493\", 		\"type\": 2, 		\"scale\": 1 	}, { 		\"x\": 6.0496514788285376, 		\"height\": 1.3, 		\"distance\": 6.0662632755032151, 		\"y\": -0.44862528325248213, 		\"id\": \"GrdK415507225\", 		\"type\": 0, 		\"scale\": 1.6136260312838553 	}, { 		\"x\": 15.104638933401711, 		\"height\": 1.3, 		\"distance\": 15.541661396279634, 		\"y\": 3.6596389768139339, 		\"id\": \"lEKXZ15507223\", 		\"type\": 0, 		\"scale\": 3.4191655071815195 	}, { 		\"x\": 12.029360175794499, 		\"height\": 1.3, 		\"distance\": 12.115119678511832, 		\"y\": 1.4389555930155724, 		\"id\": \"POICR15507018\", 		\"type\": 0, 		\"scale\": 2.6653263292726033 	}, { 		\"x\": -3.0053359745306834, 		\"height\": 1.3, 		\"distance\": 3.6693050897324393, 		\"y\": 2.105160608076186, 		\"id\": \"grvMs15506253\", 		\"type\": 0, 		\"scale\": 0.97603515386882889 	}, { 		\"x\": 15.104638933401711, 		\"height\": 1.3, 		\"distance\": 15.541661396279634, 		\"y\": 3.6596389768139339, 		\"id\": \"1p2KR15507222\", 		\"type\": 0, 		\"scale\": 3.4191655071815195 	}, { 		\"x\": 12.029360175794499, 		\"height\": 1.3, 		\"distance\": 12.115119678511832, 		\"y\": 1.4389555930155724, 		\"id\": \"kKOcW15507016\", 		\"type\": 0, 		\"scale\": 2.6653263292726033 	}, { 		\"x\": 15.104638933401711, 		\"height\": 1.3, 		\"distance\": 15.541661396279634, 		\"y\": 3.6596389768139339, 		\"id\": \"aHHoh15507220\", 		\"type\": 0, 		\"scale\": 3.4191655071815195 	}, { 		\"x\": 15.104638933401711, 		\"height\": 1.3, 		\"distance\": 15.541661396279634, 		\"y\": 3.6596389768139339, 		\"id\": \"Cmdxq15507221\", 		\"type\": 0, 		\"scale\": 3.4191655071815195 	}] }");
            }
            if (GUI.Button(new Rect(10, 300, 200, 40), "LayoutArtefactAnimated"))
            {
                LayoutArtefact("{\"mode\": 1, 	\"animated\": true, 	\"layouts\": [{ 		\"x\": 5.196152422706632, 		\"height\": 0, 		\"distance\": 0, 		\"y\": -3, 		\"id\": \"hw8gY1538237\", 		\"local\": false, 		\"scale\": 1 	}, { 		\"x\": -3.8314810822551317, 		\"height\": 3.5099505290418325, 		\"distance\": 0, 		\"y\": 3, 		\"id\": \"PKXYW1538220\", 		\"local\": false, 		\"scale\": 1 	}] }");
            }
            if (GUI.Button(new Rect(10, 350, 200, 40), "LayoutArtefactAnimated"))
            {
                LayoutArtefact("{\"mode\":0,\"animated\":true,\"layouts\":[{\"x\":-4.095613312041259,\"height\":1.0792694091796875,\"distance\":36.834358256095186,\"y\":-9.3400263630967419,\"id\":\"hw8gY1538237\",\"local\":false,\"scale\":10.681963894267604},{\"x\":3.0287605883830144,\"height\":1.0792694091796875,\"distance\":4.1923407726526678,\"y\":-2.8986595875134586,\"id\":\"PKXYW1538220\",\"local\":false,\"scale\":1.2157788240692735}]}");
            }
            if (GUI.Button(new Rect(10, 400, 200, 40), "RemoveArtefact"))
            {
                RemoveArtefacts("[\"0c7fK1533242\", \"Et5B61533246\"]");
                //OpenArtefact(warpList[0]);
            }
            if (GUI.Button(new Rect(10, 450, 200, 40), "CloseArtefact"))
            {
                CloseArtefact();
            }
            if (GUI.Button(new Rect(10, 500, 200, 40), "DissolveArtefact"))
            {
                DissolveArtefact("PKXYW1538220");
            }
            if (GUI.Button(new Rect(10, 550, 200, 40), "CreateArtefact"))
            {
                CreateArtefact("{\"height\":-1.5,\"style\":\"purple\",\"color\":\"#8600FA\",\"id\":\"0\",\"content\":\"jhghjgbjhg\",\"media\":3,\"layoutMode\":0,\"shape\":4,\"pos\":\"\",\"initial\":true}");
            }
            if (GUI.Button(new Rect(10, 600, 200, 40), "AddDebugDistance"))
            {
                BeginDebugDistance("hw8gY1538237");
            }
            if (GUI.Button(new Rect(10, 650, 200, 40), "EndDebugDistance"))
            {
                EndDebugDistance();
            }
            if (GUI.Button(new Rect(10, 700, 200, 40), "VanishArtefact"))
            {
                VanishArtefact();
            }
            if (GUI.Button(new Rect(10, 750, 200, 40), "ScreenCursor"))
            {
                //BeginDebugDistance("hw8gY1538237");
                ArtefactWarp match = FindInList("hw8gY1538237");
                match.artefact.initialPos.x = 4;
                match.artefact.initialPos.z = 4;
            }
        }
        */


    }
}
