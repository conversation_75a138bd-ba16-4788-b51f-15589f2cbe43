using System.Collections;
using System.Collections.Generic;
using System.Linq;

using UnityEngine;


namespace Com.SceneConsole.Arte
{
    public class PoiManager : MonoBehaviour
    {
        /// <summary>
        /// 预制件
        /// </summary>
        public PoiPlaneNode prefab;

        /// <summary>
        /// 预制件
        /// </summary>
        public PoiPlaneNode prefab2;

        /// <summary>
        /// 摄像机
        /// </summary>
        public Camera mainCamera;

        [HideInInspector]
        public bool layouting = false;
        private bool facilityHide = true;
        private Vector3 layoutPosition = Vector3.zero;
        private ArtefactLayout[] curLayouts;
        private float totlaTime = 0;
        private readonly HashSet<PoiPlaneNode> warpList = new HashSet<PoiPlaneNode>();
        // Start is called before the first frame update
        void Start()
        {

        }

        // Update is called once per frame
        void Update()
        {
            totlaTime += Time.deltaTime;
            if (totlaTime >= 0.5)
            {
                totlaTime = 0;
                foreach (var layout in curLayouts)
                {
                    PoiPlaneNode match = FindInList(layout.id);
                    //Debug.Log(match);
                    if (match != null)
                    {
                        var reletiveVector = transform.right * layout.x + transform.forward * layout.y;
                        Vector3 realPosition = layoutPosition + reletiveVector;
                        Vector3 vector = realPosition - mainCamera.transform.position;
                        vector.y = 0;

                        var curDistance = vector.magnitude;
                        var diff = Mathf.Abs(curDistance - match.Model.distance);
                        if (diff > 2 && curDistance > 2 && diff < 20)
                        {
                            match.SetDisplayDistance((int)curDistance);
                        }
                    }
                    else
                    {
#if DEBUG
                        Debug.Log(layout.id);
#endif
                    }
                }
            }
        }
        public void SwitchInPoisModeWithClean()
        {
            SwitchOutPoisModeAndClean();
            CameraManager.Instance.Mode = CameraManager.CameraMode.poi;
        }

        public void SwitchOutPoisModeAndClean()
        {
            CameraManager.Instance.Mode = CameraManager.CameraMode.normal;
            int childCount = transform.childCount;
            for (int j = 0; j < childCount; j++)
            {
                Destroy(transform.GetChild(j).gameObject);
            }
            warpList.Clear();
        }

        public void ShowPois()
        {
            CameraManager.Instance.Mode = CameraManager.CameraMode.poi;
            // Debug.Log("ShowPois233333333333333333");
        }

        public void HidePois()
        {
            // Debug.Log("HidePois2!!!!!!!!!!!!!!!!");
            CameraManager.Instance.Mode = CameraManager.CameraMode.normal;
            
            //int childCount = transform.childCount;
            //for (int j = 0; j < childCount; j++)
            //{
            //    Destroy(transform.GetChild(j).gameObject);
            //}
            //warpList.Clear();
        }

        public void HideFacility(string hide)
        {
            var arg = bool.Parse(hide);
            int childCount = transform.childCount;
            facilityHide = arg;
            //Debug.Log(arg);
            for (int j = 0; j < childCount; j++)
            {
                var childT = transform.GetChild(j);
                var isFacility = childT.GetComponent<PoiPlaneNode>().Model.facility;
                //Debug.Log(isFacility);

                if (isFacility != 0)//设施
                {
                    childT.gameObject.SetActive(!arg);
                }
                else//普通
                {
                    childT.gameObject.SetActive(arg);
                }
            }


        }
        /// <summary>
        /// 更新POI的位置 大小
        /// </summary>
        /// <param name="json"></param>
        public void LayoutPoi(string json)
        {
#if DEBUG
            Debug.Log(json);
#endif
            Layoutor layoutor = JsonUtility.FromJson<Layoutor>(json);
            LayoutDidUpdate(layoutor.layouts, layoutor.animated);

        }
        /// <summary>
        /// 更新位置大小
        /// </summary>
        private void LayoutDidUpdate(ArtefactLayout[] layouts, bool animated)
        {
            layoutPosition = mainCamera.transform.position;
            curLayouts = layouts;
            foreach (var layout in layouts)
            {
                PoiPlaneNode match = FindInList(layout.id);
                //Debug.Log(match);
                if (match != null)
                {

                    match.transform.parent = transform;
                    var reletiveVector = transform.right * layout.x + transform.forward * layout.y;
                    Vector3 vector = mainCamera.transform.position + reletiveVector;
                    vector.y = 0 + layout.height + mainCamera.transform.position.y - 1.5f;
                    match.SetDisplayDistance((int)layout.distance);
                    if (animated)
                    {
                        LeanTween.move(match.gameObject, vector, 1.2f);
                        LeanTween.value(match.gameObject, match.Scale, layout.scale * 0.3f, 1.0f).setEase(LeanTweenType.easeOutCubic).setOnUpdate((float val) =>
                            {
                                match.Scale = val;
                            });

                    }
                    else
                    {
                        match.transform.position = vector;
                        match.Scale = layout.scale * 0.3f;
                    }
                    //Debug.Log(vector.ToString());
                }
                else
                {
#if DEBUG
                    Debug.Log(layout.id);
#endif

                }
            }
            layouting = true;
            LeanTween.delayedCall(2.0f, () =>
            {
                layouting = false;
            });
        }

        /// <summary>
        /// 重置本地poi的位置
        /// </summary>
        public void ResetLocalPoisPosition()
        {
            layoutPosition = Vector3.zero;
            curLayouts = null;
            var tf = Camera.main.transform;
            foreach (var item in warpList)
            {
                if (item.transform.position != Vector3.zero)
                {
                    var trans = tf.InverseTransformPoint(item.transform.position);
                    item.transform.position = trans;
                }
            }
        }

        /// <summary>
        /// 添加Pois
        /// </summary>
        /// <param name="json"></param>
        public void AddPois(string json)
        {
#if DEBUG
            Debug.Log(json);
#endif
            PoiUnity[] list = JsonHelper.FromJson<PoiUnity>(json);
            for (int i = 0; i < list.Length; i++)
            {
                var model = list[i];
                CreatePois(model);
            }
        }

        /// <summary>
        /// 移除Pois
        /// </summary>
        /// <param name="json"></param>
        public void RemovePois(string json)
        {
#if DEBUG
            Debug.Log(json);
#endif
            string[] list = JsonHelper.FromJson<string>(json);

            var artis = FindSetInList(list);
            foreach (var item in artis)
            {
                warpList.Remove(item);
                Destroy(item.gameObject);
            }
        }

        /// <summary>
        /// PoiPlaneNode
        /// </summary>
        /// <param name="poi"></param>
        /// <returns></returns>
        private PoiPlaneNode CreatePois(PoiUnity poi)
        {
            Vector3 targetPos = mainCamera.transform.position;
            targetPos.y += 2;

            PoiPlaneNode warp;
            if (poi.facility == 0)
            {
                warp = Instantiate(prefab, targetPos, Quaternion.identity);

            }
            else
            {
                warp = Instantiate(prefab2, targetPos, Quaternion.identity);
            }
            warp.Model = poi;
            //判断是否添加成功
            if (warpList.Add(warp))
            {
                warp.transform.SetParent(transform);
                if (poi.facility != 0)//设施
                {
                    warp.gameObject.SetActive(!facilityHide);
                }
                else//普通
                {
                    warp.gameObject.SetActive(facilityHide);
                }
                return warp;
            }
            else
            {
                Destroy(warp.gameObject);
            }

            return null;
        }

        private PoiPlaneNode FindInList(string id)
        {
            foreach (var item in warpList)
            {
                if (item.Model.id == id)
                {
                    return item;
                }
            }
            return null;
        }

        private HashSet<PoiPlaneNode> FindSetInList(string[] ids)
        {
            var set = new HashSet<PoiPlaneNode>();
            foreach (var item in warpList)
            {
                if (ids.Contains(item.Model.id))
                {
                    set.Add(item);
                }
            }
            return set;
        }


        /*
        private void OnGUI()
        {
            if (GUI.Button(new Rect(10, 200, 200, 40), "CreatePoi"))
            {
                
                AddPois("[{\"id\":\"B0FFJBCZ6K\",\"district\":\"朝阳区\",\"adcode\":\"\",\"postcode\":\"\",\"province\":\"北京市\",\"typecode\":\"060000\",\"type\":\"购物服务;购物相关场所;购物相关场所\",\"address\":\"将台路乙2号海润国际公寓\",\"location\":{\"longitude\":116.48317,\"latitude\":39.975963999999998},\"pcode\":\"\",\"city\":\"北京市\",\"tel\":\"\",\"citycode\":\"\",\"distance\":65,\"website\":\"\",\"email\":\"\",\"name\":\"公建商业\"},{\"id\":\"B0FFF76EZI\",\"district\":\"朝阳区\",\"adcode\":\"\",\"postcode\":\"\",\"province\":\"北京市\",\"typecode\":\"190400\",\"type\":\"地名地址信息;门牌信息;门牌信息\",\"address\":\"芳园西路酒仙桥\",\"location\":{\"longitude\":116.483285,\"latitude\":39.976129},\"pcode\":\"\",\"city\":\"北京市\",\"tel\":\"\",\"citycode\":\"\",\"distance\":65,\"website\":\"\",\"email\":\"\",\"name\":\"将台路乙2号院1-9号\"}]");
            }

            if (GUI.Button(new Rect(10, 300, 200, 40), "LayoutArtefactAnimated"))
            {
                LayoutPoi("{\"mode\": 1, 	\"animated\": true, 	\"layouts\": [{ 		\"x\": 5.196152422706632, 		\"height\": 0, 		\"distance\": 0, 		\"y\": -3, 		\"id\": \"hw8gY1538237\", 		\"local\": false, 		\"scale\": 1 	}, { 		\"x\": -3.8314810822551317, 		\"height\": 3.5099505290418325, 		\"distance\": 0, 		\"y\": 3, 		\"id\": \"PKXYW1538220\", 		\"local\": false, 		\"scale\": 1 	}] }");
            }

            if (GUI.Button(new Rect(10, 400, 200, 40), "RemovePoi"))
            {
                RemovePois("[\"B0FFJBCZ6K\", \"B0FFF76EZI\"]");

            }

        }
        */

    }
}
