using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class HideBorderInBack : MonoBehaviour
{
    public GameObject border;
    // Start is called before the first frame update
    void Start()
    {
        
    }

    // Update is called once per frame
    void Update()
    {
        // 检查主相机和border是否存在
        if (Camera.main == null || border == null)
            return;

        // 计算从物体到相机的方向向量
        Vector3 directionToCamera = (Camera.main.transform.position - transform.position).normalized;

        // 使用点积判断相机是否在物体的正Z方向
        // transform.forward 表示物体的正Z方向（前方）
        float dotProduct = Vector3.Dot(transform.forward, directionToCamera);

        // 如果点积为正，说明相机在物体的正Z方向，显示border
        // 如果点积为负，说明相机在物体的负Z方向，隐藏border
        bool shouldShowBorder = dotProduct > 0;

        // 只有当状态需要改变时才设置，避免不必要的调用
        if (border.activeSelf != shouldShowBorder)
        {
            border.SetActive(shouldShowBorder);
        }
    }
}
