using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class HideBorderInBack : MonoBehaviour
{
    public GameObject border;
    // Start is called before the first frame update
    void Start()
    {
        
    }

    // Update is called once per frame
    void Update()
    {
        // 检查主相机和border是否存在
        if (Camera.main == null || border == null)
            return;

        // 将相机的世界坐标位置转换到物体的本地坐标系中
        Vector3 cameraLocalPosition = transform.InverseTransformPoint(Camera.main.transform.position);

        // 检查相机在物体本地坐标系中的Z坐标
        // 如果Z > 0，说明相机在物体的正Z方向，隐藏border
        // 如果Z < 0，说明相机在物体的负Z方向，显示border
        bool shouldShowBorder = cameraLocalPosition.z < 0;

        // 只有当状态需要改变时才设置，避免不必要的调用
        if (border.activeSelf != shouldShowBorder)
        {
            border.SetActive(shouldShowBorder);
        }
    }
}
