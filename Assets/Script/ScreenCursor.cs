using System.Collections;
using System.Collections.Generic;

using UnityEngine;
using UnityEngine.UI;

namespace Com.SceneConsole.Arte
{
    // 屏幕游标
    [DisallowMultipleComponent]
    public class ScreenCursor : MonoBehaviour
    {
        public Transform target;
        [HideInInspector]
        public Image image;
        //游标固定在中间
        static readonly bool fixCenterY = true;

        //浮动
        static readonly bool driftAround = true;

        private RectTransform cursorPos;
        private RectTransform driftPos;
        private float scale;

        private float deltaTime;

        private void Awake()
        {
            var cursor = GameObject.Find("Cursor");
            cursorPos = cursor.GetComponent<RectTransform>();
            driftPos = cursor.transform.GetChild(0).GetComponent<RectTransform>();
            image = cursor.GetComponentInChildren<Image>();
            scale = GameObject.Find("Canvas").GetComponent<Canvas>().scaleFactor;
        }

        private void Update()
        {
            if (CameraManager.Instance.Mode == CameraManager.CameraMode.paint)
            {
                image.enabled = false;
                return;
            }
            var pos = Camera.main.WorldToScreenPoint(transform.position);
            if (target != null)
            {
                pos = Camera.main.WorldToScreenPoint(target.position);
            }
            var targetPos = new Vector3();
            bool isEnabled;
            var marginX = 40 * scale;
            var topMargin = 84 * scale;
            if (Screen.safeArea.y > 0)
            {
                topMargin = 108 * scale;
            }
            if (pos.z > 0) //屏幕前
            {
                if (pos.x < Screen.width / 2f)
                {
                    targetPos.x = Mathf.Max(marginX, pos.x);
                    cursorPos.rotation = new Quaternion(0, 0, 0, 1);
                    isEnabled = pos.x < 0;
                }
                else
                {
                    targetPos.x = Mathf.Min(Screen.width - marginX, pos.x);
                    cursorPos.rotation = new Quaternion(0, 1, 0, 0);
                    isEnabled = pos.x > Screen.width;
                }
                if (!isEnabled)
                {
                    if (pos.y > Screen.height - topMargin)
                    {
                        cursorPos.rotation = Quaternion.AngleAxis(-90, Vector3.forward);
                        isEnabled = true;
                    }
                    else if (pos.y < marginX)
                    {
                        cursorPos.rotation = Quaternion.AngleAxis(90, Vector3.forward);
                        isEnabled = true;
                    }
                }
            }
            else
            {
                pos = new Vector3(-pos.x, -pos.y, pos.z);
                if (pos.x < Screen.width / 2f)
                {
                    targetPos.x = marginX;
                    cursorPos.rotation = new Quaternion(0, 0, 0, 1);
                    isEnabled = true;
                }
                else
                {
                    targetPos.x = Screen.width - marginX;
                    cursorPos.rotation = new Quaternion(0, 1, 0, 0);
                    isEnabled = true;
                }
            }
            image.enabled = isEnabled;

            if (fixCenterY)
            {
                targetPos.y = Screen.height / 2;
            }
            else
            {
                if (pos.y < marginX)
                {
                    targetPos.y = marginX;
                }
                else if (pos.y > Screen.height - topMargin)
                {
                    targetPos.y = Screen.height - topMargin;
                }
                else
                {
                    targetPos.y = pos.y;
                }
            }

            cursorPos.position = targetPos;

            deltaTime += Time.deltaTime;

            if (driftAround)
            {
                driftPos.localPosition = new Vector3(Mathf.Sin(deltaTime * 8f) * 5f, 0, 0);
            }
        }

        private void OnDestroy()
        {
            image.enabled = false;
        }

    }
}
