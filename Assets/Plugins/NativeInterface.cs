using System;
using System.IO;
using System.Runtime.InteropServices;

using UnityEngine;

namespace Com.SceneConsole.Arte
{
#if UNITY_EDITOR
    public static class NativeAPI
    {
        public static void UnityInitialize() { }

        public static void StartRecordVideo(int width, int height, int recordType) { }

        public static void SendVideoData(byte[] data, int dataLenth) { }

        public static void SendAudioData(byte[] data, int dataLenth, int channel) { }

        public static void StopRecordVideo() { }

        public static void ScreenDidShot(byte[] data, int dataLenth, int recordType) { }

        public static void DidClickArte(string id) { }

        public static void OpenArteDetail(string id) { }

        public static void OpenThematicDetail(string id) { }

        public static void OnFocusUpdated(string id, int modelType) { }

        public static void OnArtefactTracked() { }

        public static void SessionWillStart() { }

        public static void SessionDidStarted() { }

        public static void SessionInitializing() { }

        public static void Did<PERSON><PERSON><PERSON><PERSON>wer(string text) { }

        public static void LocalPublishArtiLayout(string json) { }

        public static void StartPlayAudio(string url, string cover) { }

        public static void PausePlayAudio(string url) { }

        public static void StopPlayAudio(string url) { }

        public static void WillPlayMedia() { }

        public static void didTapPoi(string poiID) { }
        
        public static void EasyARSessionWillStart() { }

        public static void EasyARSessionDidStarted() { }

        public static void EasyARMegaBlockLocalizationStatusChanged(int status) { }
        
        public static void NaviTargetChangeWithName(string name) { }
        
        public static void OnHittestWithName(string name, int mainType, int hasARTarget) { }
    }
#elif UNITY_IOS
    public static class NativeAPI
    {
        [DllImport("__Internal")]
        public static extern void UnityInitialize();

        [DllImport("__Internal")]
        public static extern void StartRecordVideo(int width, int height, int recordType);

        [DllImport("__Internal")]
        public static extern void SendVideoData(byte[] data, int dataLenth);

        [DllImport("__Internal")]
        public static extern void SendAudioData(byte[] data, int dataLenth, int channel);
        
        [DllImport("__Internal")]
        public static extern void StopRecordVideo();

        [DllImport("__Internal")]
        public static extern void ScreenDidShot(byte[] data, int dataLenth, int recordType);

        [DllImport("__Internal")]
        public static extern void DidClickArte(string id);

        [DllImport("__Internal")]
        public static extern void OpenArteDetail(string id);

        [DllImport("__Internal")]
        public static extern void OpenThematicDetail(string id);

         [DllImport("__Internal")]
        public static extern void OnFocusUpdated(string id, int modelType);

        [DllImport("__Internal")]
        public static extern void OnArtefactTracked();

        [DllImport("__Internal")]
        public static extern void SessionWillStart();

        [DllImport("__Internal")]
        public static extern void SessionDidStarted();

        [DllImport("__Internal")]
        public static extern void SessionInitializing();

        [DllImport("__Internal")]
        public static extern void DidOpenAnswer(string text);

        [DllImport("__Internal")]
        public static extern void LocalPublishArtiLayout(string json);
        
        [DllImport("__Internal")]
        public static extern void StartPlayAudio(string url, string cover);

        [DllImport("__Internal")]
        public static extern void PausePlayAudio(string url);

        [DllImport("__Internal")]
        public static extern void StopPlayAudio(string url);
        
        [DllImport("__Internal")]
        public static extern void WillPlayMedia();

        [DllImport("__Internal")]
        public static extern void didTapPoi(string poiId);

        [DllImport("__Internal")]
        public static extern void EasyARSessionWillStart();

        [DllImport("__Internal")]
        public static extern void EasyARSessionDidStarted();

        [DllImport("__Internal")]
        public static extern void EasyARMegaBlockLocalizationStatusChanged(int status);
        
        [DllImport("__Internal")]
        public static extern void NaviTargetChangeWithName(string name);
        
        [DllImport("__Internal")]
        public static extern void OnHittestWithName(string name, int mainType, int hasARTarget);
    }
#elif UNITY_ANDROID
    public static class NativeAPI
    {
        [DllImport("RecordSDK")]
        public static extern void initVideoBufferProvider(int width, int height);

        [DllImport("RecordSDK")]
        public static extern void initAudioBufferProvider(int length);

        [DllImport("RecordSDK")]
        public static extern void cleanBufferProvider();

        [DllImport("RecordSDK")]
        public static extern void copyVideoBuffer2Cyc(byte[] buffer, int len);

        [DllImport("RecordSDK")]
        public static extern void copyAudioBuffer2Cyc(byte[] buffer, int len);

        private static AndroidJavaObject javaActivity;

        public static void UnityInitialize()
        {
            InitActivity();
            try
            {
                javaActivity.Call("UnityInitialize");
            }
            catch (Exception ex)
            {
                Debug.Log(ex.Message);
            }
        }

        public static void StartRecordVideo(int width, int height, int recordType)
        {
            InitActivity();
            initVideoBufferProvider(width, height);
            initAudioBufferProvider(4096);
            try
            {
                javaActivity.Call("StartRecordVideo", width, height, recordType);
            }
            catch (Exception ex)
            {
                Debug.Log(ex.Message);
            }
        }

        public static void SendVideoData(byte[] data, int dataLenth)
        {
            copyVideoBuffer2Cyc(data, dataLenth);
        }

        public static void SendAudioData(byte[] data, int dataLenth, int channel)
        {
            copyAudioBuffer2Cyc(data, dataLenth);
        }

        public static void StopRecordVideo()
        {
            InitActivity();
            try
            {
                javaActivity.Call("StopRecordVideo");
            }
            catch (Exception ex)
            {
                Debug.Log(ex.Message);
            }
            cleanBufferProvider();
        }

        public static void ScreenDidShot(byte[] data, int dataLenth, int recordType)
        {
            InitActivity();
            javaActivity.Call("ScreenDidShot", data, dataLenth, recordType);
        }

        public static void LoadWebImage(string url) { }

        public static void DidClickArte(string id)
        {
            InitActivity();
            try
            {
                javaActivity.Call("DidClickArte", id);
            }
            catch (Exception ex)
            {
                Debug.Log(ex.Message);
            }
        }

        public static void OpenArteDetail(string id)
        {
            InitActivity();
            try
            {
                javaActivity.Call("OpenArteDetail", id);
            }
            catch (Exception ex)
            {
                Debug.Log(ex.Message);
            }
        }

        public static void OpenThematicDetail(string id)
        {
            InitActivity();
            try
            {
                javaActivity.Call("OpenThematicDetail", id);
            }
            catch (Exception ex)
            {
                Debug.Log(ex.Message);
            }
        }

        public static void OnLocationUpdated(string location) { }

        public static void OnFocusUpdated(string id, int modelType)
        {
            InitActivity();
            try
            {
                javaActivity.Call("OnFocusUpdated", id, modelType);
            }
            catch (Exception ex)
            {
                Debug.Log(ex.Message);
            }
        }

        public static void OnArtefactTracked()
        {
            InitActivity();
            try
            {
                javaActivity.Call("OnArtefactTracked");
            }
            catch (Exception ex)
            {
                Debug.Log(ex.Message);
            }
        }

        public static void OnPaintLengthUpdated(string length)
        {
            InitActivity();
            try
            {
                javaActivity.Call("OnPaintLengthUpdated", length);
            }
            catch (Exception ex)
            {
                Debug.Log(ex.Message);
            }
        }

        public static void OnPaintStepUpdated(string step)
        {
            InitActivity();
            try
            {
                javaActivity.Call("OnPaintStepUpdated", step);
            }
            catch (Exception ex)
            {
                Debug.Log(ex.Message);
            }
        }

        public static void SessionInitializing()
        {
            InitActivity();
            try
            {
                javaActivity.Call("SessionInitializing");
            }
            catch (Exception ex)
            {
                Debug.Log(ex.Message);
            }
        }

        public static void SessionWillStart()
        {
            InitActivity();
            try
            {
                javaActivity.Call("SessionWillStart");
            }
            catch (Exception ex)
            {
                Debug.Log(ex.Message);
            }
        }

        public static void SessionDidStarted()
        {
            InitActivity();
            try
            {
                javaActivity.Call("SessionDidStarted");
            }
            catch (Exception ex)
            {
                Debug.Log(ex.Message);
            }
        }

        public static void DidOpenAnswer(string text)
        {
            InitActivity();
            try
            {
                javaActivity.Call("DidOpenAnswer", text);
            }
            catch (Exception ex)
            {
                Debug.Log(ex.Message);
            }
        }

        public static void LocalPublishArtiLayout(string json)
        {
            InitActivity();
            try
            {
                javaActivity.Call("LocalPublishArtiLayout", json);
            }
            catch (Exception ex)
            {
                Debug.Log(ex.Message);
            }
        }

        public static void StartPlayAudio(string url, string cover)
        {
            InitActivity();
            try
            {
                javaActivity.Call("StartPlayAudio", url, cover);
            }
            catch (Exception ex)
            {
                Debug.Log(ex.Message);
            }
        }

        public static void PausePlayAudio(string url)
        {
            InitActivity();
            try
            {
                javaActivity.Call("PausePlayAudio", url);
            }
            catch (Exception ex)
            {
                Debug.Log(ex.Message);
            }
        }

        public static void StopPlayAudio(string url)
        {
            InitActivity();
            try
            {
                javaActivity.Call("StopPlayAudio", url);
            }
            catch (Exception ex)
            {
                Debug.Log(ex.Message);
            }
        }

        public static void WillPlayMedia()
        {
            InitActivity();
            try
            {
                javaActivity.Call("WillPlayMedia");
            }
            catch (Exception ex)
            {
                Debug.Log(ex.Message);
            }
        }

        private static void InitActivity()
        {
            if (javaActivity == null)
            {
                try
                {
                    AndroidJavaClass jc = new AndroidJavaClass("com.sceneconsole.hitta.OverrideUnityActivity");
                    javaActivity = jc.GetStatic<AndroidJavaObject>("instance");
                }
                catch (Exception ex)
                {
                    Debug.Log(ex.Message);
                }
            }
        }

        public static void didTapPoi(string url)
        {
            InitActivity();
            try
            {
                javaActivity.Call("didTapPoi", url);
            }
            catch (Exception ex)
            {
                Debug.Log(ex.Message);
            }
        }

        public static void EasyARSessionWillStart()
        {
            InitActivity();
            try
            {
                javaActivity.Call("EasyARSessionWillStart");
            }
            catch (Exception ex)
            {
                Debug.Log(ex.Message);
            }
        }

        public static void EasyARSessionDidStarted()
        {
                InitActivity();
            try
            {
                javaActivity.Call("EasyARSessionDidStarted");
            }
            catch (Exception ex)
            {
                Debug.Log(ex.Message);
            }
        }

        public static void EasyARMegaBlockLocalizationStatusChanged(int status)
        {
         InitActivity();
            try
            {
                javaActivity.Call("EasyARMegaBlockLocalizationStatusChanged", status);
            }
            catch (Exception ex)
            {
             Debug.Log(ex.Message);
             }
        }

        public static void NaviTargetChangeWithName(string name)
        {
            InitActivity();
            try
            {
                javaActivity.Call("NaviTargetChangeWithName", name);
            }
            catch (Exception ex)
            {
                Debug.Log(ex.Message);
            }
        }

        public static void OnHittestWithName(string name, int mainType, int hasARTarget)
        {
            InitActivity();
            try
            {
                javaActivity.Call("OnHittestWithName", name, hasARTarget);
            }
            catch (Exception ex)
            {
                Debug.Log(ex.Message);
            }
        }
    }
#endif

}