The TexturePacker Importer script (located in Assets/codeandweb.com/Editor)
extends your Unity editor to read sprite atlas data created with TexturePacker.

It automatically detects changed or newly added spritesheets written by TexturePacker
and (re)imports them as native Unity 2D spritesheets, so that their sprites can directly
be used in the editor. In your TexturePacker project you have to select the data format
"Unity - Texture2D sprite sheet".

Visit our tutorial page for more information:
https://www.codeandweb.com/texturepacker/unity
