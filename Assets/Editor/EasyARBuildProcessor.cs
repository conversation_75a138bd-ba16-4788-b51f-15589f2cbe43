using UnityEditor;
using UnityEditor.Callbacks;
using UnityEditor.iOS.Xcode;
using System.IO;
using UnityEngine;

public class EasyARBuildProcessor
{
    [PostProcessBuild]
    public static void OnPostProcessBuild(BuildTarget buildTarget, string pathToBuiltProject)
    {
        if (buildTarget != BuildTarget.iOS)
            return;

        return;

        // 源 framework 路径
        string sourceFrameworkPath = Path.Combine(pathToBuiltProject,
            "Frameworks/com.easyar.sense/Runtime/BindingNR/Apple/iOS/ios-arm64/easyar.framework");

        // 目标路径 - 放到 UnityFramework 内部
        string destFrameworkPath = Path.Combine(pathToBuiltProject,
            "UnityFramework/easyar.framework");

        // 确保目标目录存在
        Directory.CreateDirectory(Path.GetDirectoryName(destFrameworkPath));

        // 复制 framework
        if (Directory.Exists(sourceFrameworkPath))
        {
            if (Directory.Exists(destFrameworkPath))
            {
                Directory.Delete(destFrameworkPath, true);
            }
            FileUtil.CopyFileOrDirectory(sourceFrameworkPath, destFrameworkPath);
        }

        // 设置 Xcode 项目配置
        string pbxPath = PBXProject.GetPBXProjectPath(pathToBuiltProject);
        var pbxProject = new PBXProject();
        pbxProject.ReadFromFile(pbxPath);

        string frameworkGuid = pbxProject.GetUnityFrameworkTargetGuid();

        // 添加 framework 引用到 UnityFramework
        string fileGuid = pbxProject.AddFile("UnityFramework/easyar.framework",
            "UnityFramework/easyar.framework", PBXSourceTree.Source);

        // 将 framework 添加到 UnityFramework target
        pbxProject.AddFileToBuild(frameworkGuid, fileGuid);

        // 设置搜索路径和其他属性
        string[] buildProperties = new string[] {
            "$(inherited)",
            "$(PROJECT_DIR)/UnityFramework",
            "@executable_path",
            "@loader_path",
            "@executable_path/UnityFramework"
        };

        foreach (string prop in buildProperties)
        {
            pbxProject.AddBuildProperty(frameworkGuid, "FRAMEWORK_SEARCH_PATHS", prop);
            pbxProject.AddBuildProperty(frameworkGuid, "LD_RUNPATH_SEARCH_PATHS", prop);
        }

        // 禁用 Bitcode
        pbxProject.SetBuildProperty(frameworkGuid, "ENABLE_BITCODE", "NO");

        // 保存修改
        pbxProject.WriteToFile(pbxPath);
    }
}