{"TEXTFX_EXPORTER_VERSION": 1, "SECTION_TYPE": "MAIN", "LETTER_ANIMATIONS_DATA": {"LOOPS_DATA": [{"m_finish_at_end": false, "m_delay_first_only": true, "m_end_action_idx": 0, "m_loop_type": 1, "m_number_of_loops": 0, "m_start_action_idx": 0}], "ACTIONS_DATA": [{"m_action_type": 0, "m_ease_type": 0, "m_force_same_start_time": true, "m_letter_anchor_start": 4, "m_letter_anchor_end": 4, "m_letter_anchor_2_way": false, "m_offset_from_last": false, "m_position_axis_ease_data": {"m_override_default": false, "m_x_ease": 0, "m_y_ease": 0, "m_z_ease": 0}, "m_rotation_axis_ease_data": {"m_override_default": false, "m_x_ease": 0, "m_y_ease": 0, "m_z_ease": 0}, "m_scale_axis_ease_data": {"m_override_default": false, "m_x_ease": 0, "m_y_ease": 0, "m_z_ease": 0}, "m_colour_transition_active": true, "m_start_colour": {"m_progression": 0, "m_ease_type": 0, "m_is_offset_from_last": true, "m_to_to_bool": false, "m_unique_randoms": false, "m_animate_per": 0, "m_override_animate_per_option": false, "m_from": {"bottom_left": {"r": 1, "g": 1, "b": 1, "a": 1}, "bottom_right": {"r": 1, "g": 1, "b": 1, "a": 1}, "top_left": {"r": 0, "g": 0, "b": 0, "a": 0}, "top_right": {"r": 1, "g": 1, "b": 1, "a": 1}}, "m_to": {"bottom_left": {"r": 1, "g": 1, "b": 1, "a": 1}, "bottom_right": {"r": 1, "g": 1, "b": 1, "a": 1}, "top_left": {"r": 1, "g": 1, "b": 1, "a": 1}, "top_right": {"r": 1, "g": 1, "b": 1, "a": 1}}, "m_to_to": {"bottom_left": {"r": 1, "g": 1, "b": 1, "a": 1}, "bottom_right": {"r": 1, "g": 1, "b": 1, "a": 1}, "top_left": {"r": 1, "g": 1, "b": 1, "a": 1}, "top_right": {"r": 1, "g": 1, "b": 1, "a": 1}}, "m_use_colour_gradients": false, "m_override_alpha": false}, "m_position_transition_active": true, "m_start_pos": {"m_progression": 0, "m_ease_type": 0, "m_is_offset_from_last": false, "m_to_to_bool": false, "m_unique_randoms": false, "m_animate_per": 0, "m_override_animate_per_option": false, "m_from": {"x": 0, "y": 0, "z": 0}, "m_to": {"x": 0, "y": 0, "z": 0}, "m_to_to": {"x": 0, "y": 0, "z": 0}, "m_ease_curve_per_axis": false, "m_force_position_override": false}, "m_local_rotation_transition_active": true, "m_start_euler_rotation": {"m_progression": 0, "m_ease_type": 0, "m_is_offset_from_last": false, "m_to_to_bool": false, "m_unique_randoms": false, "m_animate_per": 0, "m_override_animate_per_option": false, "m_from": {"x": 0, "y": 0, "z": 0}, "m_to": {"x": 0, "y": 0, "z": 0}, "m_to_to": {"x": 0, "y": 0, "z": 0}, "m_ease_curve_per_axis": false}, "m_local_scale_transition_active": true, "m_start_scale": {"m_progression": 0, "m_ease_type": 0, "m_is_offset_from_last": false, "m_to_to_bool": false, "m_unique_randoms": false, "m_animate_per": 0, "m_override_animate_per_option": false, "m_from": {"x": 1, "y": 1, "z": 1}, "m_to": {"x": 1, "y": 1, "z": 1}, "m_to_to": {"x": 1, "y": 1, "z": 1}, "m_ease_curve_per_axis": false}, "m_global_rotation_transition_active": true, "m_global_start_euler_rotation": {"m_progression": 0, "m_ease_type": 0, "m_is_offset_from_last": false, "m_to_to_bool": false, "m_unique_randoms": false, "m_animate_per": 0, "m_override_animate_per_option": false, "m_from": {"x": 0, "y": 0, "z": 0}, "m_to": {"x": 0, "y": 0, "z": 0}, "m_to_to": {"x": 0, "y": 0, "z": 0}, "m_ease_curve_per_axis": false}, "m_global_scale_transition_active": true, "m_global_start_scale": {"m_progression": 0, "m_ease_type": 0, "m_is_offset_from_last": false, "m_to_to_bool": false, "m_unique_randoms": false, "m_animate_per": 0, "m_override_animate_per_option": false, "m_from": {"x": 1, "y": 1, "z": 1}, "m_to": {"x": 1, "y": 1, "z": 1}, "m_to_to": {"x": 1, "y": 1, "z": 1}, "m_ease_curve_per_axis": false}, "m_end_colour": {"m_progression": 0, "m_ease_type": 0, "m_is_offset_from_last": false, "m_to_to_bool": false, "m_unique_randoms": false, "m_animate_per": 0, "m_override_animate_per_option": false, "m_from": {"bottom_left": {"r": 0.485074609518051, "g": 0.956789612770081, "b": 1, "a": 1}, "bottom_right": {"r": 0.485074609518051, "g": 0.956789612770081, "b": 1, "a": 1}, "top_left": {"r": 0.485074609518051, "g": 0.956789612770081, "b": 1, "a": 1}, "top_right": {"r": 0.485074609518051, "g": 0.956789612770081, "b": 1, "a": 1}}, "m_to": {"bottom_left": {"r": 1, "g": 1, "b": 1, "a": 1}, "bottom_right": {"r": 1, "g": 1, "b": 1, "a": 1}, "top_left": {"r": 1, "g": 1, "b": 1, "a": 1}, "top_right": {"r": 1, "g": 1, "b": 1, "a": 1}}, "m_to_to": {"bottom_left": {"r": 1, "g": 1, "b": 1, "a": 1}, "bottom_right": {"r": 1, "g": 1, "b": 1, "a": 1}, "top_left": {"r": 1, "g": 1, "b": 1, "a": 1}, "top_right": {"r": 1, "g": 1, "b": 1, "a": 1}}, "m_use_colour_gradients": false, "m_override_alpha": false}, "m_end_pos": {"m_progression": 1, "m_ease_type": 0, "m_is_offset_from_last": false, "m_to_to_bool": false, "m_unique_randoms": true, "m_animate_per": 0, "m_override_animate_per_option": false, "m_from": {"x": 0, "y": 0, "z": 0}, "m_to": {"x": 0, "y": 0.200000002980232, "z": 0}, "m_to_to": {"x": 0, "y": 0, "z": 0}, "m_ease_curve_per_axis": false, "m_force_position_override": false}, "m_end_euler_rotation": {"m_progression": 1, "m_ease_type": 0, "m_is_offset_from_last": false, "m_to_to_bool": false, "m_unique_randoms": false, "m_animate_per": 0, "m_override_animate_per_option": false, "m_from": {"x": 0, "y": 0, "z": -10}, "m_to": {"x": 0, "y": 0, "z": 10}, "m_to_to": {"x": 0, "y": 0, "z": 0}, "m_ease_curve_per_axis": false}, "m_end_scale": {"m_progression": 0, "m_ease_type": 0, "m_is_offset_from_last": false, "m_to_to_bool": false, "m_unique_randoms": false, "m_animate_per": 0, "m_override_animate_per_option": false, "m_from": {"x": 1.10000002384186, "y": 1.10000002384186, "z": 1}, "m_to": {"x": 1, "y": 1, "z": 1}, "m_to_to": {"x": 1, "y": 1, "z": 1}, "m_ease_curve_per_axis": false}, "m_global_end_euler_rotation": {"m_progression": 0, "m_ease_type": 0, "m_is_offset_from_last": false, "m_to_to_bool": false, "m_unique_randoms": false, "m_animate_per": 0, "m_override_animate_per_option": false, "m_from": {"x": 0, "y": 0, "z": 0}, "m_to": {"x": 0, "y": 0, "z": 2}, "m_to_to": {"x": 0, "y": 0, "z": 0}, "m_ease_curve_per_axis": false}, "m_global_end_scale": {"m_progression": 0, "m_ease_type": 0, "m_is_offset_from_last": false, "m_to_to_bool": false, "m_unique_randoms": false, "m_animate_per": 0, "m_override_animate_per_option": false, "m_from": {"x": 1, "y": 1, "z": 1}, "m_to": {"x": 1, "y": 1, "z": 1}, "m_to_to": {"x": 1, "y": 1, "z": 1}, "m_ease_curve_per_axis": false}, "m_delay_progression": {"m_progression": 1, "m_ease_type": 0, "m_is_offset_from_last": false, "m_to_to_bool": false, "m_unique_randoms": false, "m_animate_per": 0, "m_override_animate_per_option": false, "m_from": 0, "m_to": 0.5, "m_to_to": 0}, "m_duration_progression": {"m_progression": 0, "m_ease_type": 0, "m_is_offset_from_last": false, "m_to_to_bool": false, "m_unique_randoms": false, "m_animate_per": 0, "m_override_animate_per_option": false, "m_from": 0.200000002980232, "m_to": 1, "m_to_to": 1}, "AUDIO_EFFECTS_DATA": [], "PARTICLE_EFFECTS_DATA": []}], "SAMPLE_NUM_LETTERS_ANIMATED": 8}, "PRESET_EFFECT_SETTINGS": [{"name": "<PERSON><PERSON> Eased", "data_type": 15, "anim_idx": 0, "action_idx": 0, "startState": true}, {"name": "Duration", "data_type": 16, "anim_idx": 0, "action_idx": 0, "startState": true}, {"name": "End Scale", "data_type": 11, "anim_idx": 0, "action_idx": 0, "startState": false}, {"name": "Highlight Colour", "data_type": 7, "anim_idx": 0, "action_idx": 0, "startState": false}, {"name": "Num. Iterations", "data_type": 20, "anim_idx": 0, "action_idx": 0, "startState": true}]}