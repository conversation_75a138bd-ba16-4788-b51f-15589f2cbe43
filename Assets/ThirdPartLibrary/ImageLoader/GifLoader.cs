using UnityEngine;
using System;
using System.IO;
using UnityEngine.UI;
using UnityImageLoader.Cache;
using UnityImageLoader.Utils;
using UnityImageLoader.Http;
using System.Collections.Generic;
using static UniGif;
using System.Collections;

namespace UnityImageLoader
{
    public class GifLoader : MonoBehaviour
    {

        static GifLoader instance;
        readonly static object locker = new object();

        AbstractHttp httpImpl;

        DataMemoryCache _memoryCache;
        DataMemoryCache memoryCache
        {
            get
            {
                if (_memoryCache == null)
                {
                    long maxMemory = Device.GetMaxMemory();
                    _memoryCache = new DataLRUMemoryCache(maxMemory / 8);
                }

                return _memoryCache;
            }

            set
            {
                _memoryCache = value;
            }
        }

        AbstractDiscCache _discCache;
        AbstractDiscCache discCache
        {
            get
            {
                if (_discCache == null)
                {
                    if (Device.IsExistSDCard())
                    {
                        string cachePath = Device.GetExternalCacheDir() + "/image_cache/";

                        if (!Directory.Exists(cachePath))
                        {
                            Directory.CreateDirectory(cachePath);
                        }

                        _discCache = new LRUDiscCache(cachePath);
                    }
                }

                return _discCache;
            }

            set
            {
                _discCache = value;
            }
        }

        GifLoader() { }

        #region public method

        public static GifLoader GetInstance()
        {
            if (instance == null)
            {
                lock (locker)
                {
                    if (instance == null)
                    {
                        instance = new GifLoader();
                    }
                }
            }

            return instance;
        }

        public GifLoader SetHttpImpl(AbstractHttp impl)
        {
            httpImpl = impl;
            return this;
        }

        public GifLoader SetMemoryCache(DataMemoryCache impl)
        {
            memoryCache = impl;
            return this;
        }

        public DataMemoryCache GetMemoryCache()
        {
            return memoryCache;
        }

        public GifLoader SetDiscCache(AbstractDiscCache impl)
        {
            discCache = impl;
            return this;
        }

        public AbstractDiscCache GetDiscCache()
        {
            return discCache;
        }

        public void RemoveCache()
        {
            discCache.RemoveCache();
            memoryCache.RemoveCache();
        }

        public void GetGif(string uri, Action<List<GifTexture>, int, int, int> callback, params DisplayOption[] option)
        {
            var manager = GifDecoderManager.Instance;
            GifLoader.GetInstance().GetTexture(uri, (data) =>
            {
                //Debug.Log("byte load" + data.Length);
                if (data != null)
                {
                    manager.AddGif(uri, data, callback);
                }
            }, option);
        }

        public void GetTexture(string uri, Action<byte[]> callback, params DisplayOption[] option)
        {
            DisplayOption opt = GetOptionFromParams(option);

            byte[] data = memoryCache.Get(uri);
            if (opt.isMemoryCache && data != null)
            {
                callback?.Invoke(data);
                return;
            }

            Uri _uri = new Uri(uri);

            if (_uri.IsFile)
            {
                if (File.Exists(uri))
                {
                    AsyncTask.GetInstance()
                             .SetDoInBackground(delegate
                             {

                                 byte[] data = File.ReadAllBytes(uri);
                                 return data;

                             })
                             .SetOnPostExecute(result =>
                             {
                                 if (result != null)
                                 {
                                     byte[] data = (byte[])result;

                                     if (opt.isMemoryCache)
                                     {
                                         memoryCache.Set(uri, data);
                                     }
                                     callback?.Invoke(data);
                                 }
                             })
                             .Excute();
                }
                else
                {
                    callback?.Invoke(null);
                }
                return;
            }

            if (opt.isDiscCache)
            {
                AsyncTask.GetInstance()
                         .SetDoInBackground(delegate
                         {
                             return discCache.Get(uri);
                         }).SetOnPostExecute(delegate (object result)
                         {
                             if (result != null)
                             {
                                 byte[] data = (byte[])result;

                                 if (opt.isMemoryCache)
                                 {
                                     memoryCache.Set(uri, data);
                                 }
                                 callback?.Invoke(data);
                             }
                             else
                             {
                                 DownloadTexture(uri, opt, callback);
                             }
                         }).Excute();
                return;
            }

            DownloadTexture(uri, opt, callback);

        }

        public void DownloadTexture(string uri, DisplayOption option, Action<byte[]> callback)
        {
            if (httpImpl == null)
            {
                httpImpl = new BestHttpImpl();
            }

            httpImpl.Get(uri, delegate (byte[] response, int statusCode, bool isSuccess)

            {
                if (isSuccess)
                {
                    if (option.isDiscCache)
                    {
                        discCache.Set(uri, response);
                    }

                    if (option.isMemoryCache)
                    {
                        memoryCache.Set(uri, response);
                    }
                    callback?.Invoke(response);
                }
                else
                {
                    callback?.Invoke(null);
                }
            });
        }

        #endregion

        private DisplayOption GetOptionFromParams(params DisplayOption[] option)
        {
            if (option == null || option.Length <= 0)
            {
                return DisplayOption.GetDefaultDisplayOption();
            }
            else
            {
                return option[0];
            }
        }

    }
}