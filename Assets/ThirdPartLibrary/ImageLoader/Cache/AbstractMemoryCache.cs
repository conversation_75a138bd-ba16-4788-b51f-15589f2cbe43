// hcq 2017/3/23
using System.Collections.Generic;

using UnityEngine;

namespace UnityImageLoader.Cache
{
    public abstract class AbstractMemoryCache : ICache<Texture2D>
    {
        const long DEFAULT_CAPACITY = 1024 * 1024 * 16;

        protected internal readonly long capacity;

        protected AbstractMemoryCache() : this(DEFAULT_CAPACITY)
        {
        }

        protected AbstractMemoryCache(long capacity)
        {
            this.capacity = capacity > 0 ? capacity : DEFAULT_CAPACITY;
        }

        public abstract Texture2D Get(string url);
        public abstract void Set(string url, Texture2D value);

        public abstract void RemoveCache();
    }

    public abstract class DataMemoryCache : ICache<byte[]>
    {
        const long DEFAULT_CAPACITY = 20 * 1024 * 1024 * 16;

        protected internal readonly long capacity;

        protected DataMemoryCache() : this(DEFAULT_CAPACITY)
        {
        }

        protected DataMemoryCache(long capacity)
        {
            this.capacity = capacity > 0 ? capacity : DEFAULT_CAPACITY;
        }

        public abstract byte[] Get(string url);
        public abstract void Set(string url, byte[] value);

        public abstract void RemoveCache();

    }
}
