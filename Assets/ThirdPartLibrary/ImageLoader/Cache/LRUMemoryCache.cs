using System.Collections.Generic;
using System.Threading;

using UnityEngine;

namespace UnityImageLoader.Cache
{
    public class LRUMemoryCache : AbstractMemoryCache
    {
        readonly ReaderWriterLockSlim lockslim;
        long size;

        readonly LinkedDictionary<string, Texture2D> linkedDictionary;

        public LRUMemoryCache(long capacity) : base(capacity)
        {
            linkedDictionary = new LinkedDictionary<string, Texture2D>();
            lockslim = new ReaderWriterLockSlim();
        }

        public override void Set(string url, Texture2D value)
        {
            if (value == null)
            {
                return;
            }

            if (linkedDictionary.TryGet(url, out Texture2D previous))
            {
                size -= ToSize(previous);
            }

            size += ToSize(value);
            linkedDictionary.Set(url, value);

            TrimToSize();
        }

        public override Texture2D Get(string url)
        {
            linkedDictionary.TryGet(url, out Texture2D result);
            return result;
        }

        public int ToSize(Texture2D value)
        {
            if (value == null)
            {
                return 0;
            }
            return value.width * value.height * 4;
        }

        public override void RemoveCache()
        {
            lockslim.EnterWriteLock();
            linkedDictionary.Clear();
            size = 0;
            lockslim.ExitWriteLock();
        }

        void TrimToSize()
        {
            while (true)
            {
                lockslim.EnterWriteLock();
                try
                {
                    if (linkedDictionary.Count == 0)
                    {
                        break;
                    }

                    if (size <= capacity)
                    {
                        break;
                    }

                    string tailKey = linkedDictionary.GetTailKey();
                    if (linkedDictionary.TryGet(tailKey, out Texture2D tailValue))
                    {
                        size -= ToSize(tailValue);
                        linkedDictionary.RemoveLast();
                    }
                }
                finally
                {
                    lockslim.ExitWriteLock();
                }
            }
        }
    }

    public class DataLRUMemoryCache : DataMemoryCache
    {
        readonly ReaderWriterLockSlim lockslim;
        long size;

        readonly LinkedDictionary<string, byte[]> linkedDictionary;

        public DataLRUMemoryCache(long capacity) : base(capacity)
        {
            linkedDictionary = new LinkedDictionary<string, byte[]>();
            lockslim = new ReaderWriterLockSlim();
        }

        public override void Set(string url, byte[] value)
        {
            if (value == null)
            {
                return;
            }

            if (linkedDictionary.TryGet(url, out byte[] previous))
            {
                size -= ToSize(previous);
            }

            size += ToSize(value);
            linkedDictionary.Set(url, value);

            TrimToSize();
        }

        public override byte[] Get(string url)
        {
            linkedDictionary.TryGet(url, out byte[] result);
            return result;
        }

        public int ToSize(byte[] value)
        {
            if (value == null)
            {
                return 0;
            }

            return value.Length * 4;
        }

        public override void RemoveCache()
        {
            lockslim.EnterWriteLock();
            linkedDictionary.Clear();
            size = 0;
            lockslim.ExitWriteLock();
        }

        void TrimToSize()
        {
            while (true)
            {
                lockslim.EnterWriteLock();
                try
                {
                    if (linkedDictionary.Count == 0)
                    {
                        break;
                    }

                    if (size <= capacity)
                    {
                        break;
                    }

                    string tailKey = linkedDictionary.GetTailKey();
                    if (linkedDictionary.TryGet(tailKey, out byte[] tailValue))
                    {
                        size -= ToSize(tailValue);
                        linkedDictionary.RemoveLast();
                    }
                }
                finally
                {
                    lockslim.ExitWriteLock();
                }
            }
        }
    }
}

