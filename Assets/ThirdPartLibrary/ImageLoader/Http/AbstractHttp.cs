// hcq 2017/3/26
using System.Collections.Generic;

namespace UnityImageLoader.Http
{
    public abstract class AbstractHttp
    {
        public delegate void OnResponseDelegate(byte[] response, int statusCode, bool isSuccess);

        protected internal int requestTimeout = 60;//unit second

        #region set params

        public abstract void SetKeepAlive(bool isKeepAlive);

        //默认60秒
        public abstract void SetRequestTimeOut(int seconds);

        #endregion

        #region request

        public abstract void Get(string url, OnResponseDelegate callback);

        #endregion
    }
}
