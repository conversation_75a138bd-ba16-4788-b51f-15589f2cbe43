// hcq 2017/3/26
using CI.HttpClient;

using System;
using System.Collections.Generic;

namespace UnityImageLoader.Http
{
    public class BestHttpImpl : AbstractHttp
    {
        private readonly HttpClient client = new HttpClient();

        public BestHttpImpl()
        {

        }

        #region set params

        //默认为true
        public override void SetKeepAlive(bool isKeepAlive)
        {
            client.KeepAlive = isKeepAlive;
        }

        public override void SetRequestTimeOut(int seconds)
        {
            client.Timeout = seconds * 1000;
        }

        #endregion

        void HandleResponse(HttpResponseMessage response, OnResponseDelegate callback)
        {
            if (callback != null)
            {
                if (response.HasContent)
                {
                    callback(response.ReadAsByteArray(), response.StatusCode.GetHashCode(), true);
                }
                else
                {
                    callback(null, -1, false);
                }
            }
        }

        public override void Get(string url, OnResponseDelegate callback)
        {
            client.Get(new Uri(url), HttpCompletionOption.AllResponseContent, (res) =>
            {
                HandleResponse(res, callback);
            });
        }

    }
}
