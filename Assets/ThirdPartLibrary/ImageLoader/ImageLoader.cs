using UnityEngine;
using System;
using System.IO;
using UnityEngine.UI;
using UnityImageLoader.Cache;
using UnityImageLoader.Utils;
using UnityImageLoader.Http;

namespace UnityImageLoader
{
    public class ImageLoader
    {
        static ImageLoader instance;
        readonly static object locker = new object();

        AbstractHttp httpImpl;

        AbstractMemoryCache _memoryCache;
        AbstractMemoryCache memoryCache
        {
            get
            {
                if (_memoryCache == null)
                {
                    long maxMemory = Device.GetMaxMemory();
                    _memoryCache = new LRUMemoryCache(maxMemory / 8);
                }

                return _memoryCache;
            }

            set
            {
                _memoryCache = value;
            }
        }

        AbstractDiscCache _discCache;
        AbstractDiscCache discCache
        {
            get
            {
                if (_discCache == null)
                {
                    if (Device.IsExistSDCard())
                    {
                        string cachePath = Device.GetExternalCacheDir() + "/image_cache/";

                        if (!Directory.Exists(cachePath))
                        {
                            Directory.CreateDirectory(cachePath);
                        }

                        _discCache = new LRUDiscCache(cachePath);
                    }
                }

                return _discCache;
            }

            set
            {
                _discCache = value;
            }
        }

        ImageLoader() { }

        #region public method

        public static ImageLoader GetInstance()
        {
            if (instance == null)
            {
                lock (locker)
                {
                    if (instance == null)
                    {
                        instance = new ImageLoader();
                    }
                }
            }

            return instance;
        }

        public ImageLoader SetHttpImpl(AbstractHttp impl)
        {
            httpImpl = impl;
            return this;
        }

        public ImageLoader SetMemoryCache(AbstractMemoryCache impl)
        {
            memoryCache = impl;
            return this;
        }

        public AbstractMemoryCache GetMemoryCache()
        {
            return memoryCache;
        }

        public ImageLoader SetDiscCache(AbstractDiscCache impl)
        {
            discCache = impl;
            return this;
        }

        public AbstractDiscCache GetDiscCache()
        {
            return discCache;
        }

        public void RemoveCache()
        {
            discCache.RemoveCache();
            memoryCache.RemoveCache();
        }

        public void GetTexture(string uri, Action<Texture2D> callback, params DisplayOption[] option)
        {
            DisplayOption opt = GetOptionFromParams(option);

            Texture2D texture = memoryCache.Get(uri);
            if (opt.isMemoryCache && texture != null)
            {
                callback?.Invoke(texture);
                return;
            }

            Uri _uri = new Uri(uri);

            if (_uri.IsFile)
            {
                if (File.Exists(uri))
                {
                    AsyncTask.GetInstance()
                             .SetDoInBackground(delegate
                             {

                                 byte[] data = File.ReadAllBytes(uri);
                                 return data;

                             })
                             .SetOnPostExecute(result =>
                             {
                                 if (result != null)
                                 {
                                     byte[] data = (byte[])result;
                                     Texture2D tex = new Texture2D(1, 1, TextureFormat.ASTC_8x8, false);
                                     tex.LoadImage((byte[])result);
                                     if (opt.isMemoryCache)
                                     {
                                         memoryCache.Set(uri, tex);
                                     }
                                     callback?.Invoke(tex);
                                 }
                             })
                             .Excute();
                }
                else
                {
                    callback?.Invoke(null);
                }
                return;
            }

            if (opt.isDiscCache)
            {
                AsyncTask.GetInstance()
                         .SetDoInBackground(delegate
                         {
                             return discCache.Get(uri);
                         }).SetOnPostExecute(delegate (object result)
                         {
                             if (result != null)
                             {
                                 byte[] data = (byte[])result;
                                 Texture2D tex = new Texture2D(1, 1, TextureFormat.ASTC_8x8, false);
                                 tex.LoadImage((byte[])result);
                                 if (opt.isMemoryCache)
                                 {
                                     memoryCache.Set(uri, tex);
                                 }
                                 callback?.Invoke(tex);
                             }
                             else
                             {
                                 DownloadTexture(uri, opt, callback);
                             }
                         }).Excute();
                return;
            }

            DownloadTexture(uri, opt, callback);

        }

        public void DownloadTexture(string uri, DisplayOption option, Action<Texture2D> callback)
        {
            if (httpImpl == null)
            {
                httpImpl = new BestHttpImpl();
            }

            httpImpl.Get(uri, delegate (byte[] response, int statusCode, bool isSuccess)

            {
                if (isSuccess)
                {
                    if (option.isDiscCache)
                    {
                        discCache.Set(uri, response);
                    }

                    Texture2D tex = new Texture2D(1, 1, TextureFormat.ASTC_8x8, false);
                    tex.LoadImage(response);
                    if (option.isMemoryCache)
                    {
                        memoryCache.Set(uri, tex);
                    }
                    callback?.Invoke(tex);
                }
                else
                {
                    callback?.Invoke(null);
                }
            });
        }

        #endregion

        private Sprite Convert2Sprite(Texture2D texture)
        {
            return Sprite.Create(texture, new Rect(0, 0, texture.width, texture.height), Vector2.zero);
        }

        private Texture2D Convert2Texture(int width, int height, byte[] data)
        {
            Texture2D texture = new Texture2D(width, height, TextureFormat.ARGB32, false);
            texture.LoadImage(data);

            return texture;
        }

        private DisplayOption GetOptionFromParams(params DisplayOption[] option)
        {
            if (option == null || option.Length <= 0)
            {
                return DisplayOption.GetDefaultDisplayOption();
            }
            else
            {
                return option[0];
            }
        }

    }
}

