// hcq 2017/3/26
using System;

using UnityEngine;

namespace UnityImageLoader.Utils
{
    public static class Device
    {
        static AndroidJavaClass GetEnvironmentClass()
        {
            return new AndroidJavaClass("android.os.Environment");
        }

        public static long GetMaxMemory()
        {
#if UNITY_ANDROID
            //return SystemInfo.systemMemorySize;
            using (AndroidJavaClass runtime = new AndroidJavaClass("java.lang.Runtime"))
            {
                using (AndroidJavaObject run = runtime.CallStatic<AndroidJavaObject>("getRuntime"))
                {
                    long maxMemory = run.Call<long>("maxMemory");
                    return maxMemory;
                }
            }
#else
            return SystemInfo.systemMemorySize;
            //throw new NotImplementedException();
#endif
        }

        public static bool IsExistSDCard()
        {
            return true;
        }

        public static string GetExternalCacheDir()
        {
            return Application.temporaryCachePath;
        }

        static AndroidJavaObject GetActivity()
        {
            using (AndroidJavaClass unityPlayerClass = new AndroidJavaClass("com.unity3d.player.UnityPlayer"))
            {
                return unityPlayerClass.GetStatic<AndroidJavaObject>("currentActivity");
            }
        }
    }
}
