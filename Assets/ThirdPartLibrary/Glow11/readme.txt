=================================
           Glow 11 
        Version 1.0.8
---------------------------------
  Copyright ©2013 Sven <PERSON>
 http://www.i-gamedev.com/glow11
     <EMAIL>
=================================

Thank you for buying Glow 11!


:: Quickstart ::

- Add "Glow 11" to the camera (use the "Component" menu, or use the "Add Component" button).
- Use one of the Shaders in "Glow11" for materials that should glow.
- For mobile targets it is recommended to enable "Use 32-bit Display Buffer" in PlayerSettings.


:: Documentation ::

A full documentation can be found at http://www.i-gamedev.com/glow11


:: Release Notes ::

1.0.8
 fixed several warnings with Unity 4.2

1.0.7
 fixed alpha blending not taking tint into account
 fixed glow when scripts modify the projecion matrix of the camera directly

1.0.6
 fixed glow not working correctly with deferred rendering

1.0.5
 added new "Advanced" blur mode for desktop
 added workaround for devices that don't support max blending

1.0.4
 fixed glow effect not working with image effects in some circumstances
 fixed glowing fog
 fixed Downsample Resolution setting when Downsample Steps is 1

1.0.3
 fixed rendering of alpha blended objects
 fixed High Precision mode not being saved
 fixed glow disappearing after loading a new level
 fixed deferred rendering

 added setting to control the rerender resolution
 added settings to control glow resolution
 
1.0
 initial release