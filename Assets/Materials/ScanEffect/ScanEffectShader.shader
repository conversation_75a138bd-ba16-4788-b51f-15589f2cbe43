Shader "Custom/ScanEffect" 
{
	Properties
	{
        // Shader properties
		_MainTex ("Texture", 2D) = "white" {}
        _ScanTex("ScanTexture",2D) = "white"{}
		_ScanRange("ScanRange",float) = 0
		_ScanWidth("ScanWidth",float) = 0
		_MeshWidth("MeshWidth",float) = 1
        _Smoothness("SeamBlending",Range(0,0.5))=0.25
		_MaxDistance("MaxDistance",float) = 100
	}
	SubShader
	{
        //Tags {"Queue" = "Transparent" "IgnoreProjector" = "True" "RenderType" = "Transparent"}
        Tags {"Queue" = "Geometry-1"}
        //LOD 100
        Pass
        {
            ColorMask 0
            Zwrite On
        }
		

        Pass
        {
            Cull Back
            ZWrite Off
            ZTest On
		    Blend SrcAlpha OneMinusSrcAlpha

            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            // make fog work
            #pragma multi_compile_fog

            #include "UnityCG.cginc"

            struct appdata
            {
                float4 vertex : POSITION;
                float2 uv : TEXCOORD0;
                float3 normal : NORMAL;
            };

            struct v2f
            {
                UNITY_FOG_COORDS(1)
                float4 vertex : SV_POSITION;
                float2 uv : TEXCOORD0;
                half3 worldPos : TEXCOORD1;
                half3 worldNormal : TEXCOORD2;
            };

            sampler2D _MainTex;
            sampler2D _ScanTex;
			float _ScanRange;
			float _ScanWidth;
			//float3 _ScanCenter;
			float _MeshWidth;
			fixed _Smoothness;
            float _MaxDistance;

            v2f vert (appdata v)
            {
                v2f o;
                o.vertex = UnityObjectToClipPos(v.vertex);
                o.uv = v.uv;//TRANSFORM_TEX(v.uv, _MainTex);

                o.worldPos = mul(unity_ObjectToWorld, v.vertex);
                o.worldNormal = UnityObjectToWorldNormal(v.normal);
                UNITY_TRANSFER_FOG(o,o.vertex);
                return o;
            }

            fixed4 frag (v2f i) : SV_Target
            {
                // sample the texture
                fixed4 col = fixed4(0,0,0,0);//tex2D(_MainTex, i.uv);

				float3 pixelWorldPos = i.worldPos;
				float pixelDistance = distance(pixelWorldPos , _WorldSpaceCameraPos);

                //计算划分出的格子的坐标位置，归一化
				float3 modulo = pixelWorldPos - _MeshWidth*floor(pixelWorldPos/_MeshWidth);
				modulo = modulo/_MeshWidth;

                half3 normal = i.worldNormal;
                normal = normalize(max(0, (abs(normal) - _Smoothness)));
				//在各个平面上计算对应的颜色吸收值
				fixed4 c_right = tex2D(_ScanTex,modulo.yz)*normal.x;
				fixed4 c_front = tex2D(_ScanTex,modulo.xy)*normal.z;
				fixed4 c_up = tex2D(_ScanTex,modulo.xz)*normal.y;
				//混合
				//fixed4 scanMeshCol = saturate((c_up + c_right + c_front));
            	
                half3 offsetColor = half3(0, 0, 0.5);
                half3 upVector = half3(0, 1, 0);
                half normalFactor = dot(normal, upVector);

                // 平滑插值版本
                half upness = saturate(normalFactor); // 向上程度 (0-1)
                // half3 upColor = half3(1, 1, 1);
            	half3 upColor = half3(14/255.0, 239/255.0, 145/255.0);

                // 非向上时的颜色：调整R和G
                half rValue = abs(normal.x) * (1 - upness); // X方向影响红色
                half gValue = abs(normal.z) * (1 - upness); // Z方向影响绿色
                half3 sideColor = half3(rValue, gValue, upness);

                // 根据向上程度混合白色和侧面颜色
                // half3 normalColor = lerp(sideColor, upColor, pow(upness, 1.2));
				// half3 normalColor = lerp(sideColor, whiteColor, upness);
            	half3 normalColor = upColor;
                fixed4 scanMeshCol = fixed4(normalColor, 1);
                //实现波纹扩散效果
				if(_ScanRange - pixelDistance > 0 && _ScanRange - pixelDistance <_ScanWidth && pixelDistance < _MaxDistance){
					fixed scanPercent = 1 - (_ScanRange - pixelDistance)/_ScanWidth;
					col = lerp(col,scanMeshCol,scanPercent);
                    return fixed4(col.xyz, saturate(scanPercent));
				}
				return col;
            }
            ENDCG
        }
	} 
}
