using System.Collections;
using System.Collections.Generic;
using UnityEngine;

[ExecuteInEditMode]
public class PosMeshScanEffect : MonoBehaviour
{
    public Material ScanMat;

    public float ScanSpeed = 5;

    public float scanTimer = -1;


    public float ScanWidth = 1;
    public float MeshWidth = 1;

    private Vector3 ScanPoint = Vector3.zero;

    void Awake()
    {
    }

    private void Update()
    {
#if UNITY_EDITOR
        return;
#endif
        
        if (scanTimer == -1)
        {
            scanTimer = 0;
            ScanPoint = Camera.main.transform.position;
        }
        scanTimer += Time.deltaTime;
        //scanTimer = 0.5f;
        ScanMat.SetVector("_ScanCenter", ScanPoint);
        ScanMat.SetFloat("_ScanRange", scanTimer*ScanSpeed);
        ScanMat.SetFloat("_ScanWidth", ScanWidth);
        ScanMat.SetFloat("_MeshWidth", MeshWidth);

    }

    public void resetScan()
    {
        scanTimer = -1;
    }

}
